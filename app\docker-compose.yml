version: "3.5"

x-tool-defaults: &app_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle


x-nginx-defaults: &nginx-defaults
  <<: *app_defaults
  healthcheck:
    test: ["C<PERSON>","nginx","-t"]

x-backend-defaults: &backend-defaults
  <<: *app_defaults
  env_file:
    - ../base/.env
    - ../app/.env
    - ../mos/.env

networks: 
  middle:
    external: true
    name: ${DOCKER_NETWORK}

services:
  frontend-bd-app:
    <<: *nginx-defaults
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/bd-app:${FRONTEND_APP_VERSION}
    environment:
      - UPSTREAM_GIS_API=${UPSTREAM_GIS_API}
      - UPSTREAM_GIS_STATIC=${UPSTREAM_GIS_STATIC}
    volumes:
      - "./config/nginx.conf:/etc/nginx/conf.d/default.conf.template:ro"
  #  ports:
  #    - "${APP_PORT}:80"

  backend-bd-app:
    <<: *backend-defaults
    image: harbor.qdbdtd.com/bjproduct/platform/services/app-backend:${BACKEND_APP_VERSION}
  #  ports:
  #    - "${APP_BACKEND_PORT}:9001"