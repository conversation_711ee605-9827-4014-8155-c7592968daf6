
## 步骤 1: 安装依赖

```bash
cd fast-soy-deploy
pip install -r requirements.txt
```

## 步骤 2: 数据库迁移

```bash
# 初始化 Alembic（如果还没有）
alembic init alembic

# 运行迁移
alembic upgrade head
```

## 步骤 3: 启动后端服务

```bash
# 开发模式
python run.py

# 或使用 uvicorn
uvicorn app:app --host 0.0.0.0 --port 9999 --reload
```

## 步骤 4: 启动前端服务

```bash
cd web
pnpm install
pnpm dev
```

## 步骤 5: 测试 API

使用 curl 或 Postman 测试 API：

```bash
# 创建容器组
curl -X POST "http://localhost:9999/api/v1/containers/groups" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-nginx",
    "description": "测试 Nginx 容器组",
    "image": "nginx:alpine",
    "replicas": 2,
    "network": "middle"
  }'

# 获取容器组列表
curl "http://localhost:9999/api/v1/containers/groups"

# 扩缩容
curl -X POST "http://localhost:9999/api/v1/containers/groups/1/scale" \
  -H "Content-Type: application/json" \
  -d '{"replicas": 3}'

## 步骤 6: 验证功能

1. **访问前端页面**: `http://localhost:3000`
2. **查看 API 文档**: `http://localhost:9999/docs`
3. **测试容器创建**: 在前端创建一个测试容器组
4. **验证扩缩容**: 修改副本数量
5. **查看日志**: 检查容器日志和部署日志
6. **WebSocket 测试**: 打开浏览器开发者工具查看实时更新

## 步骤 7: Docker 网络准备

```bash
# 创建 middle 网络（如果不存在）
docker network create middle
```
