import { request } from '../request'

export interface ContainerGroup {
  id?: number
  name: string
  description?: string
  image: string
  replicas: number
  network?: string
  environment?: Record<string, string>
  ports?: Record<string, any>
  status?: string
  created_at?: string
  containers?: Container[]
}

export interface Container {
  id: string
  name: string
  image: string
  status: string
  ports?: Record<string, any>
  created_at: string
}

export interface CreateGroupRequest {
  name: string
  description?: string
  image: string
  replicas: number
  network?: string
  environment?: Record<string, string>
  ports?: Record<string, any>
}

export interface ScaleRequest {
  replicas: number
}

export interface GetGroupsParams {
  skip?: number
  limit?: number
  status?: string
}

/** 容器组管理 API */
export const containerGroupApi = {
  /** 获取容器组列表 */
  getGroups: (params?: GetGroupsParams) => {
    return request<ContainerGroup[]>({
      url: '/containers/groups',
      method: 'get',
      params
    })
  },

  /** 获取单个容器组 */
  getGroup: (id: number) => {
    return request<ContainerGroup>({
      url: `/containers/groups/${id}`,
      method: 'get'
    })
  },

  /** 创建容器组 */
  createGroup: (data: CreateGroupRequest) => {
    return request<ContainerGroup>({
      url: '/containers/groups',
      method: 'post',
      data
    })
  },

  /** 扩缩容容器组 */
  scaleGroup: (id: number, replicas: number) => {
    return request({
      url: `/containers/groups/${id}/scale`,
      method: 'post',
      data: { replicas }
    })
  },

  /** 启动容器组 */
  startGroup: (id: number) => {
    return request({
      url: `/containers/groups/${id}/start`,
      method: 'post'
    })
  },

  /** 停止容器组 */
  stopGroup: (id: number) => {
    return request({
      url: `/containers/groups/${id}/stop`,
      method: 'post'
    })
  },

  /** 删除容器组 */
  deleteGroup: (id: number) => {
    return request({
      url: `/containers/groups/${id}`,
      method: 'delete'
    })
  },

  /** 获取容器日志 */
  getContainerLogs: (containerId: string, tail = 100) => {
    return request<{ logs: string }>({
      url: `/containers/containers/${containerId}/logs`,
      method: 'get',
      params: { tail }
    })
  },

  /** 获取容器统计信息 */
  getContainerStats: (containerId: string) => {
    return request({
      url: `/containers/containers/${containerId}/stats`,
      method: 'get'
    })
  },

  /** 获取部署日志 */
  getDeploymentLogs: (groupId: number, skip = 0, limit = 50) => {
    return request({
      url: `/containers/groups/${groupId}/logs`,
      method: 'get',
      params: { skip, limit }
    })
  }
}