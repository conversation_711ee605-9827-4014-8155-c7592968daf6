package api

import (
    "deploy-manager/internal/models"
    "deploy-manager/internal/services"
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
)

type Handler struct {
    serviceManager *services.ServiceManager
}

// NewHandler 创建API处理器
func NewHandler(serviceManager *services.ServiceManager) *Handler {
    return &Handler{
        serviceManager: serviceManager,
    }
}

// GetServices 获取所有服务
func (h *Handler) GetServices(c *gin.Context) {
    services, err := h.serviceManager.GetAllServices()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"data": services})
}

// GetServicesByGroup 根据分组获取服务
func (h *Handler) GetServicesByGroup(c *gin.Context) {
    groupName := c.<PERSON>("group")
    services, err := h.serviceManager.GetServicesByGroup(groupName)
    if err != nil {
        c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"data": services})
}

// GetService 获取单个服务
func (h *Handler) GetService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    service, err := h.serviceManager.GetService(uint(id))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"data": service})
}

// CreateService 创建服务
func (h *Handler) CreateService(c *gin.Context) {
    var service models.Service
    if err := c.ShouldBindJSON(&service); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    if err := h.serviceManager.CreateService(&service); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{"data": service})
}

// UpdateService 更新服务
func (h *Handler) UpdateService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    var service models.Service
    if err := c.ShouldBindJSON(&service); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    service.ID = uint(id)
    if err := h.serviceManager.UpdateService(&service); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"data": service})
}

// DeleteService 删除服务
func (h *Handler) DeleteService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    if err := h.serviceManager.DeleteService(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service deleted successfully"})
}

// StartService 启动服务
func (h *Handler) StartService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    if err := h.serviceManager.StartService(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service started successfully"})
}

// StopService 停止服务
func (h *Handler) StopService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    if err := h.serviceManager.StopService(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service stopped successfully"})
}

// RestartService 重启服务
func (h *Handler) RestartService(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    if err := h.serviceManager.RestartService(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service restarted successfully"})
}

// StartGroup 启动服务组
func (h *Handler) StartGroup(c *gin.Context) {
    groupName := c.Param("group")
    
    if err := h.serviceManager.StartGroup(groupName); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service group started successfully"})
}

// StopGroup 停止服务组
func (h *Handler) StopGroup(c *gin.Context) {
    groupName := c.Param("group")
    
    if err := h.serviceManager.StopGroup(groupName); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Service group stopped successfully"})
}

// GetServiceLogs 获取服务日志
func (h *Handler) GetServiceLogs(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseUint(idStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
        return
    }
    
    tail := c.DefaultQuery("tail", "100")
    
    logs, err := h.serviceManager.GetServiceLogs(uint(id), tail)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"data": logs})
}

// GetDashboard 获取仪表板数据
func (h *Handler) GetDashboard(c *gin.Context) {
    services, err := h.serviceManager.GetAllServices()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // 统计服务状态
    stats := map[string]int{
        "total":   len(services),
        "running": 0,
        "stopped": 0,
        "error":   0,
    }
    
    for _, service := range services {
        switch service.Status {
        case "running":
            stats["running"]++
        case "stopped":
            stats["stopped"]++
        default:
            stats["error"]++
        }
    }
    
    c.JSON(http.StatusOK, gin.H{
        "stats":    stats,
        "services": services,
    })
}
