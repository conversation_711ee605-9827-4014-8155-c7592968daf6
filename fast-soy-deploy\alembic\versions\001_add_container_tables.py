"""add container tables

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # 创建容器组表
    op.create_table('container_groups',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('image', sa.String(length=200), nullable=True),
        sa.Column('replicas', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('network', sa.String(length=50), nullable=True),
        sa.Column('environment', sa.JSON(), nullable=True),
        sa.Column('ports', sa.J<PERSON>(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_container_groups_id'), 'container_groups', ['id'], unique=False)
    op.create_index(op.f('ix_container_groups_name'), 'container_groups', ['name'], unique=True)
    
    # 创建容器表
    op.create_table('containers',
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=True),
        sa.Column('image', sa.String(length=200), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('node_id', sa.String(length=50), nullable=True),
        sa.Column('ports', sa.JSON(), nullable=True),
        sa.Column('environment', sa.JSON(), nullable=True),
        sa.Column('group_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['group_id'], ['container_groups.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_containers_name'), 'containers', ['name'], unique=False)
    
    # 创建部署日志表
    op.create_table('deployment_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('group_id', sa.Integer(), nullable=True),
        sa.Column('action', sa.String(length=50), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['group_id'], ['container_groups.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_deployment_logs_id'), 'deployment_logs', ['id'], unique=False)

def downgrade():
    op.drop_index(op.f('ix_deployment_logs_id'), table_name='deployment_logs')
    op.drop_table('deployment_logs')
    op.drop_index(op.f('ix_containers_name'), table_name='containers')
    op.drop_table('containers')
    op.drop_index(op.f('ix_container_groups_name'), table_name='container_groups')
    op.drop_index(op.f('ix_container_groups_id'), table_name='container_groups')
    op.drop_table('container_groups')