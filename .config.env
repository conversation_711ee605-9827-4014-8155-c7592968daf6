### dev env, if merge to bladex env, should change to prod env.###

# 矿 ID（根据 cms 矿名表进行映射的 id，每个矿都不同）
export MINECODE=199988
# 授权配置
export AUTH_SERVICE_NAME=基座平台
export AUTH_SERVICE_ID=skeleton
export AUTH_PROJECT_NAME=XX煤矿
export AUTH_PROJECT_ID=199988


#为二三维提供minio的访问地址,根据实际服务部署地址进行填写 [综合管控平台部署服务IP]
export BLADE_MINIO_HOST=***********
export THREE_GIS_URL=http://*************:20030
##洛阳GIS服务信息
export LY_GIS_HOST=***********
export SAFE_URL_HOST=http://${LY_GIS_HOST}
export SAFE_PORT=8888
export UPSTREAM_GIS_API=http://${LY_GIS_HOST}:8888
export UPSTREAM_GIS_STATIC=http://${LY_GIS_HOST}:1889
export UPSTREAM_GIS_MINIO=http://${LY_GIS_HOST}:9000
export UPSTREAM_GIS_THREE=http://${LY_GIS_HOST}:10048

## 帆软服务路径
export UPSTREAM_FINEREPORT=http://${BLADE_MINIO_HOST}:8080


## 短信服务
export SMS_URL=http://localhost:9001
export SMS_USERNAME=root
export SMS_PASSWORD=123456


#应急广播的第三方URL
export MONITOR_FACTORY_URL=http://localhost:9001


# 备份文件存放目录
BACKUP_BASE_PATH=/home/<USER>
#################################################################

# 部署视频服务
DEPLOY_VIDEO_SERVICE=false

# 部署 MOS基础服务（用户权限）
DEPLOY_MOS_SERVICE=true

# 部署IOT平台服务 (数据接入系统、预警报警消息推送、Topo)
DEPLOY_IOT_SERVICE=true

# 部署MES业务服务 (前后端)
DEPLOY_MES_SERVICE=false

# 部署业务审批流 (前后端)
DEPLOY_FLOW_SERVICE=false

# 部署GIS业务服务 (前后端)
DEPLOY_GIS_SERVICE=false

# 部署APP
DEPLOY_APP_SERVICE=false

# Docker Network 配置.
export DOCKER_NETWORK=middle

# Hasura GraphQL Engine 内存限制
export HASURA_MEMORY_LIMIT=10G

# InfluxDB 配置
export INFLUXDB_ADMIN_ENABLED=true
export INFLUXDB_HTTP_AUTH_ENABLED=true

# ssh 配置
export SSH_PASSWORD=Btdd#369#
export SSH_PORT=2222


###### espeically UPSTREAM_CMS argument ##########################
# CMS & 授权服务端地址配置
## 生产环境地址
export PUBLIC_SERVICE_PATH=39.105.136.49
export PUBLIC_SERVICE_PORT=22260
## 测试环境地址
#export PUBLIC_SERVICE_PATH=192.168.201.5
#export PUBLIC_SERVICE_PORT=22262

export UPSTREAM_CMS=http://${PUBLIC_SERVICE_PATH}:9005

# 端口转发配置
#对外暴露业务端口
export PORT_MES_BIZ=9102
export PORT_GIS_BIZ=9101
export PORT_PUSH_MSG=9104
export PORT_BASE_BACKEND=9401
export POST_WORKFLOW_BACKEND=8004
export PORT_AUTH_BACKEND=22260
export PORT_MYSQL=3306
## 文件预览路径 kkfileview
export PORT_FILE_PREVIEW=8012

# Postgres 配置
export PORT_POSTGRES=5432

# RabbitMQ 配置
export PORT_RABBITMQ=5672
export PORT_BLADEX_RABBITMQ=5672
export WEB_PORT_RABBITMQ=15672

# Data Monitor 配置
export PORT_DATA_MONITOR=9003

# 前端配置
export PORT_FRONTEND=80
# 8002
export PORT_FRONTEND_SABER=14148

