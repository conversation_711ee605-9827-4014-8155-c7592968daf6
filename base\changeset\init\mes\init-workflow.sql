-- public.blade_flow_group definition

-- Drop table

-- DROP TABLE public.blade_flow_group;

CREATE TABLE public.blade_flow_group (
	id varchar(64) NOT NULL,
	name varchar(255) NULL, -- ����
	state int2 NULL, -- ״̬
	sort int4 NULL DEFAULT 0, -- ����
	remark varchar(500) NULL, -- ��ע
	del int2 NULL, -- ɾ��
	create_time int8 NULL DEFAULT 0, -- ����ʱ��
	create_user varchar(100) NULL, -- �����˱���
	create_user_name varchar(50) NULL, -- ����������
	create_dept varchar(100) NULL, -- �������ű���
	create_dept_name varchar(50) NULL, -- ������������
	modify_time int8 NULL DEFAULT 0, -- �޸�ʱ��
	modify_user varchar(100) NULL, -- �޸��û�����
	modify_user_name varchar(50) NULL, -- �޸��û�����
	modify_dept varchar(100) NULL, -- �޸Ĳ��ű���
	modify_dept_name varchar(50) NULL, -- �޸Ĳ�������
	fdp_core int2 NOT NULL DEFAULT 0, -- �Ƿ����
	CONSTRAINT bdtd_flow_group_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_id ON public.blade_flow_group USING btree (id);
COMMENT ON TABLE public.blade_flow_group IS '���̷����';

-- Column comments

COMMENT ON COLUMN public.blade_flow_group.name IS '����';
COMMENT ON COLUMN public.blade_flow_group.state IS '״̬';
COMMENT ON COLUMN public.blade_flow_group.sort IS '����';
COMMENT ON COLUMN public.blade_flow_group.remark IS '��ע';
COMMENT ON COLUMN public.blade_flow_group.del IS 'ɾ��';
COMMENT ON COLUMN public.blade_flow_group.create_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_group.create_user IS '�����˱���';
COMMENT ON COLUMN public.blade_flow_group.create_user_name IS '����������';
COMMENT ON COLUMN public.blade_flow_group.create_dept IS '�������ű���';
COMMENT ON COLUMN public.blade_flow_group.create_dept_name IS '������������';
COMMENT ON COLUMN public.blade_flow_group.modify_time IS '�޸�ʱ��';
COMMENT ON COLUMN public.blade_flow_group.modify_user IS '�޸��û�����';
COMMENT ON COLUMN public.blade_flow_group.modify_user_name IS '�޸��û�����';
COMMENT ON COLUMN public.blade_flow_group.modify_dept IS '�޸Ĳ��ű���';
COMMENT ON COLUMN public.blade_flow_group.modify_dept_name IS '�޸Ĳ�������';
COMMENT ON COLUMN public.blade_flow_group.fdp_core IS '�Ƿ����';


-- public.blade_flow_msg definition

-- Drop table

-- DROP TABLE public.blade_flow_msg;

CREATE TABLE public.blade_flow_msg (
	id varchar(64) NOT NULL,
	title varchar(50) NOT NULL, -- ����
	bus_id varchar(64) NOT NULL, -- ҵ��ID
	"type" varchar(100) NULL, -- ����
	"content" varchar(3000) NULL, -- ����
	state int2 NULL, -- ״̬
	sort int4 NULL DEFAULT 0, -- ����
	remark varchar(500) NULL, -- ��ע
	del int2 NULL, -- ɾ��
	receive_time int8 NULL, -- ����ʱ��
	receive_user varchar(100) NULL, -- �����û�ID
	receive_user_name varchar(50) NULL, -- �����û���
	receive_dept varchar(100) NULL, -- �����û����ű���
	receive_dept_name varchar(50) NULL, -- �����û���������
	read_time int8 NULL, -- �޸�ʱ��
	fdp_core int2 NULL DEFAULT 0, -- �Ƿ����
	CONSTRAINT blade_flow_msg_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_flow_msg IS '�ֵ�����';

-- Column comments

COMMENT ON COLUMN public.blade_flow_msg.title IS '����';
COMMENT ON COLUMN public.blade_flow_msg.bus_id IS 'ҵ��ID';
COMMENT ON COLUMN public.blade_flow_msg."type" IS '����';
COMMENT ON COLUMN public.blade_flow_msg."content" IS '����';
COMMENT ON COLUMN public.blade_flow_msg.state IS '״̬';
COMMENT ON COLUMN public.blade_flow_msg.sort IS '����';
COMMENT ON COLUMN public.blade_flow_msg.remark IS '��ע';
COMMENT ON COLUMN public.blade_flow_msg.del IS 'ɾ��';
COMMENT ON COLUMN public.blade_flow_msg.receive_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_msg.receive_user IS '�����û�ID';
COMMENT ON COLUMN public.blade_flow_msg.receive_user_name IS '�����û���';
COMMENT ON COLUMN public.blade_flow_msg.receive_dept IS '�����û����ű���';
COMMENT ON COLUMN public.blade_flow_msg.receive_dept_name IS '�����û���������';
COMMENT ON COLUMN public.blade_flow_msg.read_time IS '�޸�ʱ��';
COMMENT ON COLUMN public.blade_flow_msg.fdp_core IS '�Ƿ����';


-- public.blade_flow_procdef definition

-- Drop table

-- DROP TABLE public.blade_flow_procdef;

CREATE TABLE public.blade_flow_procdef (
	id varchar(64) NOT NULL,
	name varchar(255) NULL, -- ����
	code varchar(255) NULL, -- ���
	group_id varchar(64) NULL, -- ��������ID
	"version" int4 NULL DEFAULT 0, -- �汾
	config text NULL,
	deploy_time int8 NULL, -- ����ʱ��
	icon varchar(255) NULL, -- ͼ��
	state int2 NULL, -- ״̬
	sort int4 NULL DEFAULT 0, -- ����
	remark varchar(500) NULL, -- ��ע
	del int2 NULL, -- ɾ��
	create_time int8 NULL DEFAULT 0, -- ����ʱ��
	create_user varchar(100) NULL, -- �����˱���
	create_user_name varchar(50) NULL, -- ����������
	create_dept varchar(100) NULL, -- �������ű���
	create_dept_name varchar(50) NULL, -- ������������
	modify_time int8 NULL DEFAULT 0, -- �޸�ʱ��
	modify_user varchar(100) NULL, -- �޸��û�����
	modify_user_name varchar(50) NULL, -- �޸��û�����
	modify_dept varchar(100) NULL, -- �޸Ĳ��ű���
	modify_dept_name varchar(50) NULL, -- �޸Ĳ�������
	fdp_core int2 NOT NULL DEFAULT 0, -- �Ƿ����
	CONSTRAINT blade_flow_procdef_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_flow_procdef IS '���̶����';

-- Column comments

COMMENT ON COLUMN public.blade_flow_procdef.name IS '����';
COMMENT ON COLUMN public.blade_flow_procdef.code IS '���';
COMMENT ON COLUMN public.blade_flow_procdef.group_id IS '��������ID';
COMMENT ON COLUMN public.blade_flow_procdef."version" IS '�汾';
COMMENT ON COLUMN public.blade_flow_procdef.deploy_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_procdef.icon IS 'ͼ��';
COMMENT ON COLUMN public.blade_flow_procdef.state IS '״̬';
COMMENT ON COLUMN public.blade_flow_procdef.sort IS '����';
COMMENT ON COLUMN public.blade_flow_procdef.remark IS '��ע';
COMMENT ON COLUMN public.blade_flow_procdef.del IS 'ɾ��';
COMMENT ON COLUMN public.blade_flow_procdef.create_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_procdef.create_user IS '�����˱���';
COMMENT ON COLUMN public.blade_flow_procdef.create_user_name IS '����������';
COMMENT ON COLUMN public.blade_flow_procdef.create_dept IS '�������ű���';
COMMENT ON COLUMN public.blade_flow_procdef.create_dept_name IS '������������';
COMMENT ON COLUMN public.blade_flow_procdef.modify_time IS '�޸�ʱ��';
COMMENT ON COLUMN public.blade_flow_procdef.modify_user IS '�޸��û�����';
COMMENT ON COLUMN public.blade_flow_procdef.modify_user_name IS '�޸��û�����';
COMMENT ON COLUMN public.blade_flow_procdef.modify_dept IS '�޸Ĳ��ű���';
COMMENT ON COLUMN public.blade_flow_procdef.modify_dept_name IS '�޸Ĳ�������';
COMMENT ON COLUMN public.blade_flow_procdef.fdp_core IS '�Ƿ����';


-- public.blade_flow_procinst definition

-- Drop table

-- DROP TABLE public.blade_flow_procinst;

CREATE TABLE public.blade_flow_procinst (
	id varchar(64) NOT NULL,
	procdef_id varchar(64) NULL,
	procdef_name varchar(255) NULL,
	title varchar(255) NULL, -- ����
	node_id varchar(255) NULL, -- ���̽ڵ�ID
	task_id varchar(255) NULL, -- ����ID
	start_time int8 NULL, -- ��ʼʱ��
	end_time int8 NULL, -- ����ʱ��
	duration int8 NULL, -- ����ʱ��
	flow_val text NULL, -- ���̱���
	node_list text NULL, -- �ڵ��б�
	step int4 NULL DEFAULT 0, -- ����ִ�еڼ����ڵ�
	finished int2 NULL DEFAULT 0, -- �Ƿ����
	create_time int8 NULL DEFAULT 0, -- ����ʱ��
	create_user varchar(255) NULL, -- �����˱���
	create_user_name varchar(50) NULL, -- ����������
	create_dept varchar(255) NULL, -- �������ű���
	create_dept_name varchar(255) NULL, -- ������������
	state int2 NULL, -- ״̬
	group_id varchar(255) NULL, -- ���̷���ID
	del int2 NULL, -- ɾ��
	procdef_config text NULL,
	node_name varchar(1000) NULL, -- ���̽ڵ�
	CONSTRAINT blade_flow_procinst_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_flow_procinst.title IS '����';
COMMENT ON COLUMN public.blade_flow_procinst.node_id IS '���̽ڵ�ID';
COMMENT ON COLUMN public.blade_flow_procinst.task_id IS '����ID';
COMMENT ON COLUMN public.blade_flow_procinst.start_time IS '��ʼʱ��';
COMMENT ON COLUMN public.blade_flow_procinst.end_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_procinst.duration IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_procinst.flow_val IS '���̱���';
COMMENT ON COLUMN public.blade_flow_procinst.node_list IS '�ڵ��б�';
COMMENT ON COLUMN public.blade_flow_procinst.step IS '����ִ�еڼ����ڵ�';
COMMENT ON COLUMN public.blade_flow_procinst.finished IS '�Ƿ����';
COMMENT ON COLUMN public.blade_flow_procinst.create_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_procinst.create_user IS '�����˱���';
COMMENT ON COLUMN public.blade_flow_procinst.create_user_name IS '����������';
COMMENT ON COLUMN public.blade_flow_procinst.create_dept IS '�������ű���';
COMMENT ON COLUMN public.blade_flow_procinst.create_dept_name IS '������������';
COMMENT ON COLUMN public.blade_flow_procinst.state IS '״̬';
COMMENT ON COLUMN public.blade_flow_procinst.group_id IS '���̷���ID';
COMMENT ON COLUMN public.blade_flow_procinst.del IS 'ɾ��';
COMMENT ON COLUMN public.blade_flow_procinst.node_name IS '���̽ڵ�';


-- public.blade_flow_relation definition

-- Drop table

-- DROP TABLE public.blade_flow_relation;

CREATE TABLE public.blade_flow_relation (
	id varchar(255) NOT NULL, -- ����ID
	work_user_id varchar(255) NULL, -- �û�������ID
	sign_id varchar(255) NULL, -- ǩ�»�ǩ��ID
	"type" varchar(255) NULL, -- ���ͣ�1.���� 2.��Ա������
	is_delete varchar(1) NULL, -- �Ƿ�ɾ���� 0 �� 1��
	obj_list text NULL, -- ʵ����json
	CONSTRAINT flow_relation_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_flow_relation.id IS '����ID';
COMMENT ON COLUMN public.blade_flow_relation.work_user_id IS '�û�������ID';
COMMENT ON COLUMN public.blade_flow_relation.sign_id IS 'ǩ�»�ǩ��ID';
COMMENT ON COLUMN public.blade_flow_relation."type" IS '���ͣ�1.���� 2.��Ա������';
COMMENT ON COLUMN public.blade_flow_relation.is_delete IS '�Ƿ�ɾ���� 0 �� 1��';
COMMENT ON COLUMN public.blade_flow_relation.obj_list IS 'ʵ����json';


-- public.blade_flow_task definition

-- Drop table

-- DROP TABLE public.blade_flow_task;

CREATE TABLE public.blade_flow_task (
	id varchar(64) NOT NULL,
	node_type varchar(255) NULL,
	node_id varchar(255) NULL,
	node_name varchar(255) NULL, -- �ڵ�����
	node_code varchar(255) NULL, -- �ڵ����
	procinst_id varchar(64) NULL,
	assignee varchar(255) NULL,
	create_time varchar(255) NULL,
	claim_time varchar(255) NULL,
	member_count int2 NULL DEFAULT 1,
	un_complete_num int2 NULL DEFAULT 1,
	agree_num int2 NULL,
	counter_sign int2 NULL DEFAULT 1, -- �Ƿ��ǩ
	finished int2 NULL DEFAULT 0,
	state int2 NULL,
	CONSTRAINT blade_flow_task_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_flow_task.node_name IS '�ڵ�����';
COMMENT ON COLUMN public.blade_flow_task.node_code IS '�ڵ����';
COMMENT ON COLUMN public.blade_flow_task.counter_sign IS '�Ƿ��ǩ';


-- public.blade_flow_task_log definition

-- Drop table

-- DROP TABLE public.blade_flow_task_log;

CREATE TABLE public.blade_flow_task_log (
	id int8 NOT NULL,
	create_user int8 NULL,
	create_dept int8 NULL,
	create_time timestamp(6) NULL,
	update_user int8 NULL,
	update_time timestamp(6) NULL,
	status int4 NULL,
	is_deleted int4 NULL,
	step varchar(100) NULL,
	sort int4 NULL,
	assignee_user_id int8 NULL,
	assignee_user_name varchar(20) NULL,
	"comment" varchar(500) NULL,
	deal_time timestamp(6) NULL,
	task_id varchar(100) NULL,
	process_instance_id varchar(100) NULL,
	task_status varchar(20) NULL,
	CONSTRAINT blade_flow_task_log_pkey PRIMARY KEY (id)
);


-- public.blade_flow_undo definition

-- Drop table

-- DROP TABLE public.blade_flow_undo;

CREATE TABLE public.blade_flow_undo (
	id varchar(64) NOT NULL,
	user_id varchar(255) NULL,
	user_name varchar(255) NULL,
	dept_id varchar(64) NULL,
	dept_name varchar(255) NULL,
	procinst_id varchar(64) NULL,
	task_id varchar(64) NULL,
	"result" int2 NULL DEFAULT 0, -- �������
	finished int2 NULL DEFAULT 0,
	create_time int8 NULL DEFAULT 0, -- ����ʱ��
	assertion_time int8 NULL DEFAULT 0, -- ����ʱ��
	suggest text NULL, -- ���
	assignor_undo_id varchar(64) NULL, -- ί��ִ����ID
	appendix_url text NULL, -- ����url
	flow_val text NULL, -- �����˱���ֵ
	form_data text NULL, -- �����˱�������
	CONSTRAINT blade_flow_undo_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_flow_undo."result" IS '�������';
COMMENT ON COLUMN public.blade_flow_undo.create_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_undo.assertion_time IS '����ʱ��';
COMMENT ON COLUMN public.blade_flow_undo.suggest IS '���';
COMMENT ON COLUMN public.blade_flow_undo.assignor_undo_id IS 'ί��ִ����ID';
COMMENT ON COLUMN public.blade_flow_undo.appendix_url IS '����url';
COMMENT ON COLUMN public.blade_flow_undo.flow_val IS '�����˱���ֵ';
COMMENT ON COLUMN public.blade_flow_undo.form_data IS '�����˱�������';