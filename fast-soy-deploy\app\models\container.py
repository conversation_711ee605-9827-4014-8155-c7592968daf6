from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class ContainerGroup(Base):
    __tablename__ = "container_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    description = Column(Text)
    image = Column(String(200))
    replicas = Column(Integer, default=1)
    status = Column(String(20), default="stopped")  # running, stopped, error, creating
    network = Column(String(50), default="middle")
    environment = Column(JSON)  # 环境变量
    ports = Column(JSON)  # 端口映射
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联容器
    containers = relationship("Container", back_populates="group", cascade="all, delete-orphan")

class Container(Base):
    __tablename__ = "containers"
    
    id = Column(String(64), primary_key=True)  # Docker 容器 ID
    name = Column(String(100), index=True)
    image = Column(String(200))
    status = Column(String(20))  # running, stopped, error
    node_id = Column(String(50))
    ports = Column(JSON)  # 端口映射
    environment = Column(JSON)  # 环境变量
    group_id = Column(Integer, ForeignKey("container_groups.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联容器组
    group = relationship("ContainerGroup", back_populates="containers")

class DeploymentLog(Base):
    __tablename__ = "deployment_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey("container_groups.id"))
    action = Column(String(50))  # deploy, scale, stop, start
    status = Column(String(20))  # success, failed, running
    message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

