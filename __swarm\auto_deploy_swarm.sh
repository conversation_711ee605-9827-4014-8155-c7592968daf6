#!/bin/bash

# Swarm 初始化
init_swarm() {
  if ! docker info | grep -q "Swarm: active"; then
    echo_yellow "初始化 Docker Swarm"
    docker swarm init --advertise-addr $(hostname -I | awk '{print $1}')
  fi
  
  # 创建 overlay 网络
  docker network create --driver overlay --attachable middle || true
}

# 部署 Stack
deploy_stack() {
  local stack_name=$1
  local compose_file=$2
  
  echo_yellow "部署 Stack: $stack_name"
  docker stack deploy -c $compose_file $stack_name
  
  # 等待服务启动
  wait_stack_ready $stack_name
}

# 等待 Stack 就绪
wait_stack_ready() {
  local stack_name=$1
  local timeout=300
  
  while [ $timeout -gt 0 ]; do
    local running=$(docker stack services $stack_name --format "{{.Replicas}}" | grep -c "1/1")
    local total=$(docker stack services $stack_name --quiet | wc -l)
    
    if [ "$running" -eq "$total" ]; then
      echo_green "Stack $stack_name 已就绪"
      return 0
    fi
    
    echo "等待 Stack $stack_name 启动... ($running/$total)"
    sleep 10
    ((timeout-=10))
  done
  
  echo_error "Stack $stack_name 启动超时"
  return 1
}

# 滚动更新
rolling_update() {
  local service_name=$1
  local image=$2
  
  echo_yellow "滚动更新服务: $service_name"
  docker service update --image $image $service_name
}

# 扩缩容
scale_service() {
  local service_name=$1
  local replicas=$2
  
  echo_yellow "扩缩容服务: $service_name 到 $replicas 个副本"
  docker service scale $service_name=$replicas
}