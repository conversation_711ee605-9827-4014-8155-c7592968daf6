########################################################
#                                                      #
#                  TDengine Configuration              #
#   Any questions, <NAME_EMAIL>   #
#                                                      #
########################################################

# first fully qualified domain name (FQDN) for TDengine system
firstEp                   tdengine:6030

# local fully qualified domain name (FQDN)
fqdn                      tdengine

# first port number for the connection (12 continuous UDP/TCP port number are used)
# serverPort                6030

# log file's directory
# logDir                    /var/log/taos

# data file's directory
# dataDir                   /var/lib/taos

# temporary file's directory
# tempDir                   /tmp/

# the arbitrator's fully qualified domain name (FQDN) for TDengine system, for cluster only
# arbitrator                arbitrator_hostname:6042

# number of threads per CPU core
# numOfThreadsPerCore       1.0

# number of threads to commit cache data
# numOfCommitThreads        4

# the proportion of total CPU cores available for query processing
# 2.0: the query threads will be set to double of the CPU cores.
# 1.0: all CPU cores are available for query processing [default].
# 0.5: only half of the CPU cores are available for query.
# 0.0: only one core available.
# ratioOfQueryCores        1.0

# the last_row/first/last aggregator will not change the original column name in the result fields
keepColumnName            1

# number of management nodes in the system
# numOfMnodes               1

# enable/disable backuping vnode directory when removing vnode
# vnodeBak                  1

# enable/disable installation / usage report
# telemetryReporting        1

# enable/disable load balancing
# balance                   1

# role for dnode. 0 - any, 1 - mnode, 2 - dnode
# role                      0

# max timer control blocks
# maxTmrCtrl                512

# time interval of system monitor, seconds
# monitorInterval           30

# number of seconds allowed for a dnode to be offline, for cluster only
# offlineThreshold          864000

# RPC re-try timer, millisecond
# rpcTimer                  300

# RPC maximum time for ack, seconds.
# rpcMaxTime                600

# time interval of dnode status reporting to mnode, seconds, for cluster only
# statusInterval            1

# time interval of heart beat from shell to dnode, seconds
# shellActivityTimer        3

# minimum sliding window time, milli-second
# minSlidingTime            10

# minimum time window, milli-second
# minIntervalTime           10

# maximum delay before launching a stream computation, milli-second
# maxStreamCompDelay        20000

# maximum delay before launching a stream computation for the first time, milli-second
# maxFirstStreamCompDelay   10000

# retry delay when a stream computation fails, milli-second
# retryStreamCompDelay      10

# the delayed time for launching a stream computation, from 0.1(default, 10% of whole computing time window) to 0.9
# streamCompDelayRatio      0.1

# max number of vgroups per db, 0 means configured automatically
# maxVgroupsPerDb           0

# max number of tables per vnode
# maxTablesPerVnode         1000000

# cache block size (Mbyte)
# cache                     16

# number of cache blocks per vnode
blocks                    9

# number of days per DB file
# days                  10

# number of days to keep DB file
# keep                  3650

# minimum rows of records in file block
# minRows               100

# maximum rows of records in file block
# maxRows               4096

# the number of acknowledgments required for successful data writing
# quorum                1

# enable/disable compression
# comp                  2

# write ahead log (WAL) level, 0: no wal; 1: write wal, but no fysnc; 2: write wal, and call fsync
# walLevel              1

# if walLevel is set to 2, the cycle of fsync being executed, if set to 0, fsync is called right away
# fsync                 3000

# number of replications, for cluster only
# replica               1

# the compressed rpc message, option:
#  -1 (no compression)
#   0 (all message compressed),
# > 0 (rpc message body which larger than this value will be compressed)
# compressMsgSize       -1

# query retrieved column data compression option:
#  -1 (no compression)
#   0 (all retrieved column data compressed),
# > 0 (any retrieved column size greater than this value all data will be compressed.)
# compressColData       -1

# max length of an SQL
# maxSQLLength          65480
maxSQLLength          1000000
# max length of WildCards
# maxWildCardsLength    100

# the maximum number of records allowed for super table time sorting
# maxNumOfOrderedRes    100000

# system time zone
timezone              Asia/Shanghai (CST, +0800)
# system time zone (for windows 10)
# timezone              UTC-8

# system locale
# locale                en_US.UTF-8
locale                zh_CN.UTF-8

# default system charset
charset               UTF-8

# max number of connections allowed in dnode
# maxShellConns         5000

# max number of connections allowed in client
# maxConnections        5000

# stop writing logs when the disk size of the log folder is less than this value
# minimalLogDirGB       1.0

# stop writing temporary files when the disk size of the tmp folder is less than this value
# minimalTmpDirGB       1.0

# if disk free space is less than this value, taosd service exit directly within startup process
# minimalDataDirGB      2.0

# One mnode is equal to the number of vnode consumed
# mnodeEqualVnodeNum    4

# enbale/disable http service
# http                  1

# enable/disable system monitor
# monitor               1

# enable/disable recording the SQL statements via restful interface
# httpEnableRecordSql   0

# number of threads used to process http requests
# httpMaxThreads        2

# maximum number of rows returned by the restful interface
# restfulRowLimit       10240
# database name must be specified in restful interface if the following parameter is set, off by default
# httpDbNameMandatory 1

# http keep alive, default is 30 seconds
# httpKeepAlive   30000

# The following parameter is used to limit the maximum number of lines in log files.
# max number of lines per log filters
# numOfLogLines         10000000

# enable/disable async log
# asyncLog              1

# time of keeping log files, days
# logKeepDays           0


# The following parameters are used for debug purpose only.
# debugFlag 8 bits mask: FILE-SCREEN-UNUSED-HeartBeat-DUMP-TRACE_WARN-ERROR
# 131: output warning and error
# 135: output debug, warning and error
# 143: output trace, debug, warning and error to log
# 199: output debug, warning and error to both screen and file
# 207: output trace, debug, warning and error to both screen and file

# debug flag for all log type, take effect when non-zero value
debugFlag             131

# debug flag for meta management messages
# mDebugFlag            135

# debug flag for dnode messages
# dDebugFlag            135

# debug flag for sync module
# sDebugFlag            135

# debug flag for WAL
# wDebugFlag            135

# debug flag for SDB
# sdbDebugFlag          135

# debug flag for RPC
# rpcDebugFlag          131

# debug flag for TAOS TIMER
# tmrDebugFlag          131

# debug flag for TDengine client
# cDebugFlag            131

# debug flag for JNI
# jniDebugFlag          131

# debug flag for storage
# uDebugFlag            131

# debug flag for http server
# httpDebugFlag         131

# debug flag for monitor
# monDebugFlag          131

# debug flag for query
# qDebugFlag            131

# debug flag for vnode
# vDebugFlag            131

# debug flag for TSDB
# tsdbDebugFlag         131

# debug flag for continue query
# cqDebugFlag           131

# enable/disable recording the SQL in taos client
# enableRecordSql    0

# generate core file when service crash
# enableCoreFile        1

# maximum display width of binary and nchar fields in the shell. The parts exceeding this limit will be hidden
# maxBinaryDisplayWidth 30

# enable/disable stream (continuous query)
# stream                1

# in retrieve blocking model, only in 50% query threads will be used in query processing in dnode
# retrieveBlockingModel    0

# the maximum allowed query buffer size in MB during query processing for each data node
# -1 no limit (default)
# 0  no query allowed, queries are disabled
# queryBufferSize         -1

# percent of redundant data in tsdb meta will compact meta data,0 means donot compact
# tsdbMetaCompactRatio    0

# default string type used for storing JSON String, options can be binary/nchar, default is nchar
# defaultJSONStrType      nchar

# force TCP transmission
# rpcForceTcp        0

# unit MB. Flush vnode wal file if walSize > walFlushSize and walSize > cache*0.5*blocks
# walFlushSize         1024

# unit Hour. Latency of data migration
# keepTimeOffset     0