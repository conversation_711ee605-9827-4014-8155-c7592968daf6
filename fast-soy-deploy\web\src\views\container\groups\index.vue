<template>
  <div class="container-groups">
    <NCard title="容器组管理" :bordered="false" size="small" class="rounded-8px shadow-sm">
      <template #header-extra>
        <NButton type="primary" @click="showCreateModal = true">
          <template #icon>
            <icon-ic-round-add />
          </template>
          创建容器组
        </NButton>
      </template>
      
      <NDataTable
        :columns="columns"
        :data="containerGroups"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        size="small"
        class="sm:h-full"
      />
    </NCard>
    
    <!-- 创建容器组模态框 -->
    <CreateGroupModal 
      v-model:visible="showCreateModal" 
      @success="handleCreateSuccess"
    />
    
    <!-- 容器详情模态框 -->
    <ContainerDetailModal
      v-model:visible="showDetailModal"
      :group-id="selectedGroupId"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, h } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NTag, NSpace, NInputNumber, useMessage } from 'naive-ui'
import { containerGroupApi } from '@/service/api'
import CreateGroupModal from './components/CreateGroupModal.vue'
import ContainerDetailModal from './components/ContainerDetailModal.vue'

const message = useMessage()

interface ContainerGroup {
  id: number
  name: string
  description: string
  image: string
  replicas: number
  status: string
  created_at: string
  containers: any[]
}

const containerGroups = ref<ContainerGroup[]>([])
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedGroupId = ref<number>()

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
    loadContainerGroups()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadContainerGroups()
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    running: 'success',
    stopped: 'default',
    error: 'error',
    creating: 'warning'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap = {
    running: '运行中',
    stopped: '已停止',
    error: '错误',
    creating: '创建中'
  }
  return statusMap[status] || status
}

const handleScaleGroup = async (groupId: number, replicas: number) => {
  try {
    await containerGroupApi.scaleGroup(groupId, replicas)
    message.success('扩缩容任务已提交')
    setTimeout(() => loadContainerGroups(), 2000) // 2秒后刷新
  } catch (error) {
    message.error('扩缩容失败')
  }
}

const handleStartGroup = async (groupId: number) => {
  try {
    await containerGroupApi.startGroup(groupId)
    message.success('启动任务已提交')
    setTimeout(() => loadContainerGroups(), 2000)
  } catch (error) {
    message.error('启动失败')
  }
}

const handleStopGroup = async (groupId: number) => {
  try {
    await containerGroupApi.stopGroup(groupId)
    message.success('停止任务已提交')
    setTimeout(() => loadContainerGroups(), 2000)
  } catch (error) {
    message.error('停止失败')
  }
}

const handleDeleteGroup = async (groupId: number) => {
  try {
    await containerGroupApi.deleteGroup(groupId)
    message.success('删除成功')
    loadContainerGroups()
  } catch (error) {
    message.error('删除失败')
  }
}

const showContainerDetail = (groupId: number) => {
  selectedGroupId.value = groupId
  showDetailModal.value = true
}

const columns: DataTableColumns<ContainerGroup> = [
  {
    title: '名称',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '镜像',
    key: 'image',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '副本数',
    key: 'replicas',
    width: 120,
    render(row) {
      return h(NInputNumber, {
        value: row.replicas,
        min: 0,
        max: 20,
        size: 'small',
        onUpdateValue: (value: number) => {
          handleScaleGroup(row.id, value)
        }
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: getStatusType(row.status),
        size: 'small'
      }, {
        default: () => getStatusText(row.status)
      })
    }
  },
  {
    title: '容器数',
    key: 'containers',
    width: 80,
    render(row) {
      return row.containers?.length || 0
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            onClick: () => showContainerDetail(row.id)
          }, { default: () => '详情' }),
          
          h(NButton, {
            size: 'small',
            type: 'success',
            disabled: row.status === 'running',
            onClick: () => handleStartGroup(row.id)
          }, { default: () => '启动' }),
          
          h(NButton, {
            size: 'small',
            type: 'warning',
            disabled: row.status === 'stopped',
            onClick: () => handleStopGroup(row.id)
          }, { default: () => '停止' }),
          
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDeleteGroup(row.id)
          }, { default: () => '删除' })
        ]
      })
    }
  }
]

const loadContainerGroups = async () => {
  loading.value = true
  try {
    const response = await containerGroupApi.getGroups({
      skip: (pagination.page - 1) * pagination.pageSize,
      limit: pagination.pageSize
    })
    containerGroups.value = response.data
  } catch (error) {
    message.error('加载容器组失败')
  } finally {
    loading.value = false
  }
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  loadContainerGroups()
}

onMounted(() => {
  loadContainerGroups()
})
</script>