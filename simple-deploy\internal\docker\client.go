package docker

import (
    "context"
    "deploy-manager/internal/models"
    "fmt"
    "io"
    "strings"
    "time"

    "github.com/docker/docker/api/types"
    "github.com/docker/docker/api/types/container"
    "github.com/docker/docker/api/types/network"
    "github.com/docker/docker/client"
    "github.com/docker/go-connections/nat"
)

type Client struct {
    cli *client.Client
    ctx context.Context
}

// NewClient 创建Docker客户端
func NewClient() (*Client, error) {
    cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
    if err != nil {
        return nil, fmt.Errorf("failed to create docker client: %v", err)
    }
    
    return &Client{
        cli: cli,
        ctx: context.Background(),
    }, nil
}

// StartService 启动服务
func (c *Client) StartService(service *models.Service) error {
    // 检查镜像是否存在，不存在则拉取
    if err := c.ensureImage(service.Image + ":" + service.Version); err != nil {
        return fmt.Errorf("failed to ensure image: %v", err)
    }
    
    // 创建容器配置
    containerConfig := c.buildContainerConfig(service)
    hostConfig := c.buildHostConfig(service)
    networkConfig := c.buildNetworkConfig(service)
    
    // 创建容器
    resp, err := c.cli.ContainerCreate(
        c.ctx,
        containerConfig,
        hostConfig,
        networkConfig,
        nil,
        service.Name,
    )
    if err != nil {
        return fmt.Errorf("failed to create container: %v", err)
    }
    
    // 启动容器
    if err := c.cli.ContainerStart(c.ctx, resp.ID, types.ContainerStartOptions{}); err != nil {
        return fmt.Errorf("failed to start container: %v", err)
    }
    
    service.ContainerID = resp.ID
    service.Status = "running"
    
    return nil
}

// StopService 停止服务
func (c *Client) StopService(service *models.Service) error {
    if service.ContainerID == "" {
        return fmt.Errorf("container ID is empty")
    }
    
    timeout := 30 * time.Second
    if err := c.cli.ContainerStop(c.ctx, service.ContainerID, &timeout); err != nil {
        return fmt.Errorf("failed to stop container: %v", err)
    }
    
    service.Status = "stopped"
    return nil
}

// RemoveService 删除服务容器
func (c *Client) RemoveService(service *models.Service) error {
    if service.ContainerID == "" {
        return fmt.Errorf("container ID is empty")
    }
    
    if err := c.cli.ContainerRemove(c.ctx, service.ContainerID, types.ContainerRemoveOptions{
        Force: true,
    }); err != nil {
        return fmt.Errorf("failed to remove container: %v", err)
    }
    
    service.ContainerID = ""
    service.Status = "stopped"
    return nil
}

// GetServiceStatus 获取服务状态
func (c *Client) GetServiceStatus(service *models.Service) (string, error) {
    if service.ContainerID == "" {
        return "stopped", nil
    }
    
    inspect, err := c.cli.ContainerInspect(c.ctx, service.ContainerID)
    if err != nil {
        if client.IsErrNotFound(err) {
            return "stopped", nil
        }
        return "error", err
    }
    
    if inspect.State.Running {
        return "running", nil
    } else if inspect.State.Restarting {
        return "restarting", nil
    } else if inspect.State.Paused {
        return "paused", nil
    } else if inspect.State.Dead {
        return "dead", nil
    }
    
    return "stopped", nil
}

// GetServiceLogs 获取服务日志
func (c *Client) GetServiceLogs(service *models.Service, tail string) (string, error) {
    if service.ContainerID == "" {
        return "", fmt.Errorf("container ID is empty")
    }
    
    options := types.ContainerLogsOptions{
        ShowStdout: true,
        ShowStderr: true,
        Tail:       tail,
        Timestamps: true,
    }
    
    logs, err := c.cli.ContainerLogs(c.ctx, service.ContainerID, options)
    if err != nil {
        return "", fmt.Errorf("failed to get container logs: %v", err)
    }
    defer logs.Close()
    
    logBytes, err := io.ReadAll(logs)
    if err != nil {
        return "", fmt.Errorf("failed to read logs: %v", err)
    }
    
    return string(logBytes), nil
}

// ensureImage 确保镜像存在
func (c *Client) ensureImage(imageName string) error {
    _, _, err := c.cli.ImageInspectWithRaw(c.ctx, imageName)
    if err != nil {
        if client.IsErrNotFound(err) {
            // 镜像不存在，拉取镜像
            reader, err := c.cli.ImagePull(c.ctx, imageName, types.ImagePullOptions{})
            if err != nil {
                return err
            }
            defer reader.Close()
            
            // 等待拉取完成
            _, err = io.ReadAll(reader)
            return err
        }
        return err
    }
    return nil
}

// buildContainerConfig 构建容器配置
func (c *Client) buildContainerConfig(service *models.Service) *container.Config {
    config := &container.Config{
        Image: service.Image + ":" + service.Version,
        Env:   make([]string, 0),
    }
    
    // 设置环境变量
    for key, value := range service.Environment {
        config.Env = append(config.Env, fmt.Sprintf("%s=%s", key, value))
    }
    
    // 设置命令
    if service.Command != "" {
        config.Cmd = strings.Fields(service.Command)
    }
    
    // 设置端口
    if len(service.Ports) > 0 {
        config.ExposedPorts = make(nat.PortSet)
        for _, port := range service.Ports {
            containerPort := nat.Port(port.ContainerPort + "/" + port.Protocol)
            config.ExposedPorts[containerPort] = struct{}{}
        }
    }
    
    return config
}

// buildHostConfig 构建主机配置
func (c *Client) buildHostConfig(service *models.Service) *container.HostConfig {
    hostConfig := &container.HostConfig{
        RestartPolicy: container.RestartPolicy{
            Name: service.RestartPolicy,
        },
    }
    
    // 设置端口映射
    if len(service.Ports) > 0 {
        hostConfig.PortBindings = make(nat.PortMap)
        for _, port := range service.Ports {
            containerPort := nat.Port(port.ContainerPort + "/" + port.Protocol)
            hostConfig.PortBindings[containerPort] = []nat.PortBinding{
                {
                    HostIP:   "0.0.0.0",
                    HostPort: port.HostPort,
                },
            }
        }
    }
    
    // 设置卷映射
    if len(service.Volumes) > 0 {
        hostConfig.Binds = make([]string, 0)
        for _, volume := range service.Volumes {
            bind := fmt.Sprintf("%s:%s:%s", volume.HostPath, volume.ContainerPath, volume.Mode)
            hostConfig.Binds = append(hostConfig.Binds, bind)
        }
    }
    
    return hostConfig
}

// buildNetworkConfig 构建网络配置
func (c *Client) buildNetworkConfig(service *models.Service) *network.NetworkingConfig {
    return &network.NetworkingConfig{
        EndpointsConfig: map[string]*network.EndpointSettings{
            "middle": {}, // 默认连接到middle网络
        },
    }
}

// Close 关闭Docker客户端
func (c *Client) Close() error {
    return c.cli.Close()
}
