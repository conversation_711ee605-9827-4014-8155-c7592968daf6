{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "6.6.1"}, {"type": "panel", "id": "grafana-piechart-panel", "name": "Pie Chart", "version": "1.3.8"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "在同一看板下展示TCP,ICMP,HTTPS的状态监控，优化各图表展示效果，支持多服务同时展示。", "editable": true, "gnetId": 9965, "graphTooltip": 0, "id": null, "iteration": 1585038563302, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 0, "fillGradient": 0, "gridPos": {"h": 13, "w": 10, "x": 0, "y": 0}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "probe_duration_seconds{job=~'$job',instance=~'$targets'}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{env}}: {{ instance }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "检测总耗时 $targets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "${DS_PROMETHEUS}", "fontSize": "80%", "format": "short", "gridPos": {"h": 6, "w": 8, "x": 10, "y": 0}, "id": 300, "interval": null, "legend": {"percentage": true, "show": true, "sort": null, "sortDesc": null, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "options": {}, "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "count_values('value',probe_http_status_code{job=~'$job'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "http status {{ value }}", "refId": "A"}], "title": "HTTP检测类服务状态统计", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "${DS_PROMETHEUS}", "fontSize": "80%", "format": "short", "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0}, "id": 2412, "interval": null, "legend": {"percentage": true, "show": true, "sort": null, "sortDesc": null, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "options": {}, "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": " count_values('value',probe_success{job=~'$job'})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ value }}", "refId": "A"}], "title": "服务连通性统计", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 14, "x": 10, "y": 6}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "probe_icmp_duration_seconds{job=~'$job',instance=~'$targets'}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} {{phase}}", "refId": "A"}, {"expr": "probe_http_duration_seconds{job=~'$job',instance=~'$targets'}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} {{phase}}", "refId": "B"}, {"expr": "sum by (phase) (probe_http_duration_seconds{job=~'$job',instance=~'$targets'})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{phase}}", "refId": "C"}, {"expr": "sum by (phase) (probe_icmp_duration_seconds{job=~'$job',instance=~'$targets'})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{phase}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ICMP/HTTPS检测类 阶段耗时 $targets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fontSize": "100%", "gridPos": {"h": 12, "w": 24, "x": 0, "y": 13}, "id": 2414, "links": [], "options": {}, "pageSize": null, "repeat": null, "repeatDirection": "h", "scroll": true, "showHeader": true, "sort": {"col": 1, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "env", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "service", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": "cell", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "#56A64B"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "mappingType": 1, "pattern": "ip", "preserveFormat": false, "sanitize": false, "thresholds": ["0", "1"], "type": "string", "unit": "short", "valueMaps": []}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "port", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "连通性", "align": "auto", "colorMode": "cell", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #A", "preserveFormat": false, "thresholds": ["0", "1"], "type": "string", "unit": "short", "valueMaps": [{"text": "在线", "value": "1"}, {"text": "离线", "value": "0"}]}, {"alias": "SSL", "align": "auto", "colorMode": "cell", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #B", "thresholds": ["0", "1"], "type": "string", "unit": "short", "valueMaps": [{"text": "OK", "value": "1"}, {"text": "NO", "value": "0"}]}, {"alias": "HTTP", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #C", "preserveFormat": true, "thresholds": ["0", "499"], "type": "string", "unit": "short"}, {"alias": "SSL Cert Expiry", "align": "auto", "colorMode": "cell", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "MMMM D, YYYY LT", "decimals": 2, "mappingType": 1, "pattern": "Value #D", "thresholds": ["1000000", "2500000"], "type": "number", "unit": "s"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "probe_success{instance=~\"$targets\"} - 0", "format": "table", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"expr": "probe_http_ssl{instance=~\"$targets\"} - 0", "format": "table", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "B"}, {"expr": "probe_http_status_code{instance=~\"$targets\"} - 0", "format": "table", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "C"}, {"expr": "probe_ssl_earliest_cert_expiry{instance=~\"$targets\"}-time()", "format": "table", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "D"}, {"refId": "F"}], "title": "$job Blackbox 状态明细", "transform": "table", "type": "table"}], "refresh": false, "schemaVersion": 22, "style": "dark", "tags": ["StarsL.cn", "Prometheus", "blackbox_exporter"], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(up{job=~\"ICMP探测|HTTP探测|TCP探测\"},job)", "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "job", "options": [], "query": "label_values(up{job=~\"ICMP探测|HTTP探测|TCP探测\"},job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(up{job=~'$job'},env)", "hide": 0, "includeAll": true, "label": "env", "multi": true, "name": "env", "options": [], "query": "label_values(up{job=~'$job'},env)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(up{job=~'$job',env=~'$env'},service)", "hide": 0, "includeAll": true, "label": "service", "multi": true, "name": "service", "options": [], "query": "label_values(up{job=~'$job',env=~'$env'},service)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(up{job=~'$job',env=~'$env',service=~'$service'},instance)", "hide": 0, "includeAll": true, "label": "targets", "multi": true, "name": "targets", "options": [], "query": "label_values(up{job=~'$job',env=~'$env',service=~'$service'},instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"auto": true, "auto_count": 10, "auto_min": "10s", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, "hide": 0, "label": "interval", "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "5s", "value": "5s"}, {"selected": false, "text": "10s", "value": "10s"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "5s,10s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"datasource": null, "filters": [], "hide": 0, "label": "", "name": "Filters", "skipUrlSync": false, "type": "adhoc"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "HTTP/TCP/ICMP监控", "uid": "ynUxXflik", "version": 17}