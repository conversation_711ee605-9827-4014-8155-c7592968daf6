from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ContainerGroupBase(BaseModel):
    name: str = Field(..., description="容器组名称")
    description: Optional[str] = Field(None, description="描述")
    image: str = Field(..., description="Docker 镜像")
    replicas: int = Field(1, ge=0, le=20, description="副本数量")
    network: str = Field("middle", description="网络名称")
    environment: Optional[Dict[str, str]] = Field(None, description="环境变量")
    ports: Optional[Dict[str, Any]] = Field(None, description="端口映射")

class ContainerGroupCreate(ContainerGroupBase):
    pass

class ContainerGroupUpdate(BaseModel):
    description: Optional[str] = None
    replicas: Optional[int] = Field(None, ge=0, le=20)
    environment: Optional[Dict[str, str]] = None
    ports: Optional[Dict[str, Any]] = None

class ContainerResponse(BaseModel):
    id: str
    name: str
    image: str
    status: str
    ports: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class ContainerGroupResponse(ContainerGroupBase):
    id: int
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    containers: List[ContainerResponse] = []
    
    class Config:
        from_attributes = True

class ScaleRequest(BaseModel):
    replicas: int = Field(..., ge=0, le=20, description="目标副本数")

class LogResponse(BaseModel):
    logs: str
    container_id: str
    timestamp: datetime

class ContainerStatsResponse(BaseModel):
    container_id: str
    cpu_percent: float
    memory_usage: int
    memory_limit: int
    network_rx: int
    network_tx: int
    timestamp: datetime
