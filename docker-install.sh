#!/bin/bash

# docker安装

echo "============================ docker安装开始 ============================"

which docker
if [ $? -ne 0 ]; then
    yum remove -y docker docker-common docker-selinux docker-engine
    yum install -y yum-utils device-mapper-persistent-data lvm2
    yum-config-manager --add-repo http://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
    yum makecache fast
    yum install -y docker-ce
# 镜像加速
cat > /etc/docker/daemon.json << EOF
{
    "log-driver":"json-file",
    "data-root": "/data/docker",
    "log-opts":{ "max-size" :"50m","max-file":"3"},
	"registry-mirrors": ["https://5gmkcj2a.mirror.aliyuncs.com"]
}
EOF
    systemctl daemon-reload
    systemctl enable docker
    systemctl restart docker
fi

which docker-compose
if [ $? -ne 0 ]; then
    yum install -y python3-pip
    pip3 install docker-compose
fi

echo "============================ docker安装完成 ============================"

exit 0

