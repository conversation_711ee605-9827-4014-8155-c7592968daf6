version: "3.5"

x-base-defaults: &base_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes: 
  base-rabbitmq:
    external: true
  base-red-node:
    external: true
#  base-red-node_1:
#    external: true
#  base-red-node_2:
#    external: true
#  base-red-node_3:
#    external: true
  base-redis:
    external: true
  base-postgres:
    external: true
  base-influxdb:
    external: true
  bladex-minio:
    external: true
  base-ftp:
    external: true
  base-business-postgres:
    external: true
  base-mysql:
    external: true
  base-tdengine-data:
    external: true
  base-tdengine-log:
    external: true
#  base-emqx-data:
#    external: true
#  base-emqx-etc:
#    external: true
#  base-emqx-log:
#    external: true

services:
  # PostgreSQL
  postgres:
    <<: *base_defaults
    #image: harbor.qdbdtd.com/middleware/postgres:10.23-alpine3.16
    #image: postgres:13.1
    image: harbor2.qdbdtd.com:8088/middleware/postgis:${PG_VERSION}
    container_name: base_postgres
    volumes:
      - "base-postgres:/var/lib/postgresql/data"
      #- "./config/postgres_init.sql:/docker-entrypoint-initdb.d/postgres_init.sql:ro"
    environment:
      - POSTGRES_USER=${POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DATABASE}
    #ports:
    #  - ${PORT_POSTGRES}:5432

  # PostgreSQL
  business-postgres:
    <<: *base_defaults
    #image: harbor.qdbdtd.com/middleware/postgres:10.23-alpine3.16
    image: harbor2.qdbdtd.com:8088/middleware/postgis:${PG_VERSION}
    container_name: base_business_postgres
    volumes:
      - "base-business-postgres:/var/lib/postgresql/data"
    environment:
      - POSTGRES_USER=${BLADEX_POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${BLADEX_POSTGRES_PASSWORD}
      - POSTGRES_DB=${BLADEX_POSTGRES_DATABASE}
    #ports:
    #  - ${BLADEX_POSTGRES_WEB_PORT}:5432

  # Node-RED
  rednode:
    << : *base_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
    environment:
      - NDGW_RABBITMQ_USERNAME=${RABBITMQ_USERNAME}
      - NDGW_RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
    volumes:
      - "base-red-node:/app/data"
      - "base-ftp:/app/vsftp:rw"
      - "/home/<USER>/file_sync:/app/rsync:rw"
    #ports:
    #  - "1880:1880"
#  base_rednode_2:
#    << : *base_defaults
#    container_name: base_rednode_2
#    image: harbor.qdbdtd.com/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
#    volumes:
#      - "base-red-node_1:/app/data"
#      - "/home/<USER>/file_sync:/app/rsync:rw"
#  base_rednode_3:
#    << : *base_defaults
#    container_name: base_rednode_3
#    image: harbor.qdbdtd.com/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
#    volumes:
#      - "base-red-node_2:/app/data"
#      - "base-ftp:/app/vsftp:rw"
#      - "/home/<USER>/file_sync/archive/sgjc:/app/rsync/archive/sgjc:rw"
#      - "/home/<USER>/file_sync/archive/swjc:/app/rsync/archive/swjc:rw"
#  base_rednode_4:
#    << : *base_defaults
#    container_name: base_rednode_4
#    image: harbor.qdbdtd.com/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
#    volumes:
#      - "base-red-node_3:/app/data"
#      - "/home/<USER>/file_sync/archive:/app/rsync/archive:rw"

  # ftp server
#  ftpserver:
#    << : *base_defaults
#    image: harbor2.qdbdtd.com:8088/middleware/pure-ftpd:1.0
#    ports:
#      - "21:21"
#      # max concurrent connection is 10, if need more, can change it
#      - "30000-30010"
#    volumes:
#      - "base-ftp:/home/<USER>"
#      - "$PWD/vsftp/passwd:/etc/pure-ftpd/passwd"
#    environment:
#      - PUBLICHOST=${FTP_PUBLIC_HOST}
#      - FTP_USER_NAME=${FTP_USERNAME}
#      - FTP_USER_PASS=${FTP_USER_PASS}
#      - FTP_USER_HOME=${FTP_USER_HOME}
#      - FTP_MAX_CLIENTS=${FTP_MAX_CLIENTS}
#      - FTP_MAX_CONNECTIONS=${FTP_MAX_CONNECTIONS}
#      - FTP_PASSIVE_PORTS=${FTP_PASSIVE_PORTS}
#      - ADDED_FLAGS=${FTP_ADDED_FLAGS}
#    restart: always
  ftpserver:
    << : *base_defaults
    image: fauria/vsftpd
    ports:
      - "20:20"
      - "21:21"
      - "34140-34146:34140-34146"
    volumes:
      - "base-ftp:/home/<USER>"
    environment:
      - FTP_USER=${FTP_USERNAME:vsftp}
      - FTP_PASS=${FTP_USER_PASS:Swf#46Ds7&}
      #- PASV_ADDRESS=127.0.0.1
      - PASV_MIN_PORT=34140
      - PASV_MAX_PORT=34146
    restart: always

  # Redis
  redis:
    << : *base_defaults
    # image: harbor2.qdbdtd.com:8088/middleware/redis:${REDIS_VERSION}
    # command: /bin/bash -c "envsubst < /usr/local/etc/redis/redis.template > /usr/local/etc/redis/redis.conf && exec redis-server /usr/local/etc/redis/redis.conf"
    image: harbor2.qdbdtd.com:8088/middleware/redis:7.4.3
    command: /bin/bash -c "cp /usr/local/etc/redis/redis.template /usr/local/etc/redis/redis.conf && redis-server /usr/local/etc/redis/redis.conf --requirepass \"$REDIS_PASSWORD\""
    volumes:
      - "base-redis:/data"
      - "./config/redis.template:/usr/local/etc/redis/redis.template:ro"
    environment: 
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    #ports:
    #  - 6379:6379

  # RabbitMQ
  rabbitmq:
    << : *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/rabbitmq:${RMQ_VERSION}
    volumes: 
      - base-rabbitmq:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    ports:
      # don't expose rabbitmq to publish
      - ${PORT_RABBITMQ}:5672
      #- ${WEB_PORT_RABBITMQ}:15672

#  # InfluxDB
#  influxdb:
#    << : *base_defaults
#    image: harbor2.qdbdtd.com:8088/middleware/influxdb:${TSDB_VERSION}
#    volumes:
#      - "base-influxdb:/var/lib/influxdb"
#      - "./config/influxdb.conf:/etc/influxdb/influxdb.conf:ro"
#    environment:
#      - INFLUXDB_DB=${INFLUXDB_DATABASE}
#      - INFLUXDB_ADMIN_USER=${INFLUXDB_USERNAME}
#      - INFLUXDB_ADMIN_PASSWORD=${INFLUXDB_PASSWORD}
#      - INFLUXDB_ADMIN_ENABLED=${INFLUXDB_ADMIN_ENABLED}
#      - INFLUXDB_HTTP_AUTH_ENABLED=${INFLUXDB_HTTP_AUTH_ENABLED}

  # MinIO
  bladex-minio:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/minio:${MINIO_VERSION}
    volumes:
      - bladex-minio:/export
    command: ['server', '/export']
    environment:
      - MINIO_ACCESS_KEY=${BLADEX_MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${BLADEX_MINIO_SECRET_KEY}
    #ports:
    #  - ${BLADEX_MINIO_PORT}:9000     

#  #kkfileview 
#  kkfileview:
#    <<: *base_defaults
#    image: harbor2.qdbdtd.com:8088/middleware/kkfileview:${KKFILE_VERSION}
#    container_name: kkfileview
#  #ports:
#  #   - ${PORT_FILE_PREVIEW}:8012

  tdengine:
    <<: *base_defaults
    container_name: tdengine
    hostname: tdengine
    #image: harbor.qdbdtd.com/middleware/tdengine:*******
    image: tdengine/tdengine:*******
    #environment:
      # 密码设定在初始化脚本中 bdtdtd / dev@123-$
      # 默认用户名为 root，密码为 taosdata
      #- TZ: Asia/Shanghai
    #ports:
    #  - "6030-6049:6030-6049/tcp"
    #  - "6030-6049:6030-6049/udp"
    #  #- "6060:6060" # 企业版内 Monitor 服务的网络端口
    volumes:
      - "./tdengine/etc/taos.cfg:/etc/taos/taos.cfg"
      #- "./tdengine/etc/taosadapter.toml:/etc/taos/taosadapter.toml"
      - "base-tdengine-data:/var/lib/taos"
      - "base-tdengine-log:/var/log/taos:rw"
      # - "./changeset/init/tsdb:/home/<USER>"

#  emqx:
#    <<: *base_defaults
#    container_name: emqx
#    image: emqx/emqx:4.4.19
#    hostname: emqx
#    environment:
#      - "EMQX_NAME=emqx"
#      #- "EMQX_HOST=node1.emqx.io"
#      #- "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
#      #- "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>, <EMAIL>]"
#    ports:
#      - "1883:1883"
#      #- "18083:18083"
#    volumes:
#      - "base-emqx-data:/opt/emqx/data"
#      - "base-emqx-etc:/opt/emqx/etc"
#      - "base-emqx-log:/opt/emqx/log"

#  kafka:
#    container_name: kafka-1
#    image: dockerhub.icu/bitnami/kafka:${KAFKA_VERSION}
#    #ports:
#    #  - ${KAFKA_ADVERTISED_PORT}:9092
#    #  - ${KAFKA_CONTROLLER_PORT}:9093
#    environment:
#      ### 通用配置
#      # 允许使用kraft，即Kafka替代Zookeeper
#      - KAFKA_ENABLE_KRAFT=yes
#      # 集群配置
#      - KAFKA_CFG_NODE_ID=1
#      # 集群地址
#      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:${KAFKA_CONTROLLER_PORT}
#      # broker.id，必须唯一，且与KAFKA_CFG_NODE_ID一致
#      - KAFKA_BROKER_ID=1
#      # kafka角色，做broker，也要做controller
#      - KAFKA_CFG_PROCESS_ROLES=controller,broker
#      # 定义kafka服务端socket监听端口（Docker内部的地址和端口）
#      # - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
#      - KAFKA_CFG_LISTENERS=SASL_PLAINTEXT://:9092,CONTROLLER://:9093
#      # 定义客户端应使用的地址和端口、外网访问地址，宿主机地址和端口，不能是0.0.0.0
#      # - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
#      - KAFKA_CFG_ADVERTISED_LISTENERS=SASL_PLAINTEXT://${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
#      # 定义安全协议
#      # - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
#      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT
#      # 客户端用户
#      - KAFKA_CLIENT_USERS=${KAFKA_CLIENT_USERS}
#      - KAFKA_CLIENT_PASSWORDS=${KAFKA_CLIENT_PASSWORDS}
#      # 控制器的监听器名称，指定供外部使用的控制类请求信息
#      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
#      # 控制器的 SASL 机制协议
#      - KAFKA_CFG_SASL_MECHANISM_CONTROLLER_PROTOCOL=PLAIN
#      # 经纪人之间的通信监听器 安全协议
#      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=SASL_PLAINTEXT
#      # 经纪人之间的 SASL 机制协议
#      - KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL=PLAIN
#      # 经纪人之间的身份验证的用户名和密码
#      - KAFKA_INTER_BROKER_USER=${KAFKA_INTER_BROKER_USER}
#      - KAFKA_INTER_BROKER_PASSWORD=${KAFKA_INTER_BROKER_PASSWORD}
#      # 设置broker最大内存，和初始内存
#      - KAFKA_HEAP_OPTS=-Xmx512M -Xms256M
#      # 使用Kafka时的集群id，集群内的Kafka都要用这个id做初始化，生成一个UUID即可(22byte)
#      - KAFKA_KRAFT_CLUSTER_ID=${KAFKA_KRAFT_CLUSTER_ID}
#      # 允许使用PLAINTEXT监听器，默认false，不建议在生产环境使用
#      - ALLOW_PLAINTEXT_LISTENER=yes
#      # 自动创建主题 允许
#      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
#  kafka-ui:
#    container_name: kafka-ui-1
#    image: dockerhub.icu/provectuslabs/kafka-ui:latest
#    hostname: kafka-ui-1
#    restart: always
#    #ports:
#    #  - ${KAFKA_UI_PORT}:8080
#    # volumes:
#    #   - /home/<USER>/etc/localtime:/etc/localtime
#    environment:
#      - TZ=Asia/Shanghai
#      - AUTH_TYPE=LOGIN_FORM
#      - SPRING_SECURITY_USER_NAME=${KAFKA_UI_USER_NAME}
#      - SPRING_SECURITY_USER_PASSWORD=${KAFKA_UI_USER_PASSWORD}
#      - DYNAMIC_CONFIG_ENABLED=${KAFKA_UI_DYNAMIC_CONFIG_ENABLED}
#      - KAFKA_CLUSTERS_0_NAME=${KAFKA_UI_DEFAULT_CLUSTER_NODE_NAME}
#      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
#      - KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL=SASL_PLAINTEXT
#      - KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM=PLAIN
#      - KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG='org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_UI_DEFAULT_CLUSTER_NODE_USERNAME}" password="${KAFKA_UI_DEFAULT_CLUSTER_NODE_PASSWORD}";'
#    # network_mode: "bridge"
#    # networks:
#    #   - yklw
#    depends_on:
#      - kafka

