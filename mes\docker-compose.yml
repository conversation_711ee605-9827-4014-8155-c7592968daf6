version: "3.5"

x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mes/.env
  healthcheck:
    test: ["CMD","nginx","-t"]

x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mes/.env

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  mes-biz-log:
    external: true

services:
  # 前端 - 业务系统
  frontend-base-business_serve:
    <<: *nginx-reset
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-business-serve:${FRONTEND_MES_VERSION}

  # 后端 mes业务系统
  backend-mes-biz:
    <<: *backend-defaults
    image: harbor.qdbdtd.com/bjproduct/platform/services/mes_biz:${BACKEND_MES_VERSION}
    environment:
      - SMS_URL=${SMS_URL}
      - SMS_USERNAME=${SMS_USERNAME}
      - SMS_PASSWORD=${SMS_PASSWORD}
    volumes:
      - "mes-biz-log:/home/<USER>/logs"
  #ports:
  #    - "${PORT_MES_BIZ}:9001"
