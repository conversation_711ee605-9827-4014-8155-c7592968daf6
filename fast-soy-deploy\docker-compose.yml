version: '3.8'

services:
  web:
    image: gplane/pnpm:9.15-node18-bullseye
    working_dir: /opt/fast-soy-admin/web
    volumes:
      - ./web:/opt/fast-soy-admin/web
    command: >
      sh -c "
        cd /opt/fast-soy-admin/web &&
        pnpm install --frozen-lockfile &&
        export NODE_OPTIONS=--max_old_space_size=4096 &&
        pnpm run build
      "
    networks:
      - internal

  nginx:
    image: nginx:alpine
    ports:
      - "1880:80"
    environment:
      - LANG=zh_CN.UTF-8
    volumes:
      - ./web/dist:/var/www/html/fast-soy-admin
      - ./deploy/web.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - web
      - redis
      - app
    restart: always
    networks:
      - internal

  app:
    image: seven45/pdm-ci:3.11-buster
    working_dir: /opt/fast-soy-admin
    command: >
      sh -c "
        pdm config pypi.url https://pypi.tuna.tsinghua.edu.cn/simple/ &&
        pdm install --frozen-lockfile --no-editable &&
        pdm run run.py
      "
    environment:
      - LANG=zh_CN.UTF-8
    volumes:
      - .:/opt/fast-soy-admin
    networks:
      - internal

  redis:
    image: redis:alpine
    networks:
      - internal
    volumes:
      - ./redis_data:/data
#    command: redis-server --requirepass yourpassword


networks:
  internal:
    driver: bridge
