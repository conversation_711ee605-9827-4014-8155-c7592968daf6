#!/bin/bash

# PostgreSQL Backup
backup_postgres () {
   echo_yellow "备份 PostgreSQL"
   source $BASE_SERVICE_PATH/.env
   BACKUP_DB_PATH=${BACKUP_BASE_PATH}/$(date "+%Y-%m-%d")/db
   mkdir -p $BACKUP_DB_PATH

   docker exec base_postgres /bin/bash -c "pg_dump -U ${POSTGRES_USERNAME} ${POSTGRES_DATABASE} > /tmp/gis.sql" && \
   docker exec base_postgres /bin/bash -c "cd /tmp && tar zcvf gis.tgz gis.sql" && \
   docker cp base_postgres:/tmp/gis.tgz ${BACKUP_DB_PATH}/pg.$(date "+%H.%M.%S").tgz && \
   docker exec base_postgres /bin/bash -c 'rm -f /tmp/gis.sql /tmp/gis.tgz'

   echo_yellow "备份mes"
   docker exec base_business_postgres /bin/bash -c "pg_dump -U ${BLADEX_POSTGRES_USERNAME} ${BLADEX_POSTGRES_DATABASE} > /tmp/mes.sql" && \
   docker exec base_business_postgres /bin/bash -c "cd /tmp && tar zcvf mes.tgz mes.sql" && \
   docker cp base_business_postgres:/tmp/mes.tgz ${BACKUP_DB_PATH}/mes.$(date "+%H.%M.%S").tgz && \
   docker exec base_business_postgres /bin/bash -c 'rm -f /tmp/mes.tgz /tmp/mes.sql'

}
