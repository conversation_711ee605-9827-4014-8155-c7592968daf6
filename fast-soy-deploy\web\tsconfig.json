{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["./src/*"], "~/*": ["./*"]}, "resolveJsonModule": true, "types": ["vite/client", "node", "unplugin-icons/types/vue", "naive-ui/volar"], "strict": true, "strictNullChecks": true, "noUnusedLocals": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true}, "include": ["./**/*.ts", "./**/*.tsx", "./**/*.vue"], "exclude": ["node_modules", "dist"]}