version: "3.8"

services:
  prometheus:
    image: prom/prometheus
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]
    configs:
      - source: prometheus_config
        target: /etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'

configs:
  prometheus_config:
    external: true
    name: prometheus_yml_v1