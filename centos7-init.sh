#！/bin/bash
###
 # @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @Date: 2024-10-24 17:44:23
 # @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @LastEditTime: 2024-11-19 13:50:02
 # @FilePath: \yklw-test_gkpt-deploy-v2.1.0\centos7-init.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
echo "Hello Boy"


#关闭防火墙
echo  -e "\033[32m 清除防火墙规则成功\033[0m"
systemctl disable firewalld
systemctl stop firewalld &> /dev/null
echo -e "\033[32m 关闭和禁用防火墙自启。\033[0m"

#ssh优化
sed -i 's#\#UseDNS yes#UseDNS no#' /etc/ssh/sshd_config  && service sshd restart
echo  -e "\033[32m ssh优化成功\033[0m"

#永久关闭SELinux
sed -i '7c SELINUX=disabled' /etc/selinux/config
setenforce 0
echo -e "\033[32m 永久关闭SELinux。\033[0m"

#系统升级
echo 'nameserver *******' >> /etc/resolv.conf
yum install -y  wget
rm -rf /etc/yum.repos.d/CentOS*
curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo
wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo
yum -y update
yum clean all
yum makecache
echo -e "\033[32m 更新yum源成功。\033[0m"

#安装基本软件
yum install -y   ntp tree vim  net-tools  iftop iotop  curl-devel expat-devel gettext-devel openssl-devel zlib-devel asciidoc gcc perl-ExtUtils-MakeMaker
echo -e "\033[32m 软件基础软件安装成功。\033[0m"


#安装测速工具speedtestcli
#wget -O speedtest-cli https://raw.githubusercontent.com/sivel/speedtest-cli/master/speedtest.py
#wget https://bintray.com/ookla/rhel/rpm -O bintray-ookla-rhel.repo
#mv bintray-ookla-rhel.repo /etc/yum.repos.d/
#yum install speedtest -y

#安装git2.x版本
#cd /usr/local/src/ && wget https://www.kernel.org/pub/software/scm/git/git-2.15.1.tar.xz
#tar -vxf git-2.15.1.tar.xz && cd git-2.15.1
#make prefix=/usr/local/git all && make prefix=/usr/local/git install
#echo "export PATH=$PATH:/usr/local/git/bin" >> /etc/profile && source /etc/profile
#git --version
#echo -e "\033[32m git2.x版本安装成功。\033[0m"


#安装docker
yum install -y yum-utils  device-mapper-persistent-data  lvm2
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
yum install docker-ce -y
systemctl enable docker
systemctl start docker
echo -e "\033[32m docker安装成功\033[0m"


#开启docker加速
cat > /etc/docker/daemon.json << EOF
{
"registry-mirrors": ["https://5gmkcj2a.mirror.aliyuncs.com"]
}
EOF
systemctl daemon-reload
service docker restart
echo "开启docker加速成功"

# 安装 docker-compose
yum -y install epel-release openssl-devel gcc
yum -y install python3-pip
pip3 install --upgrade pip
pip3 install docker-compose==1.23.2
docker-compose --version
echo -e "\033[32m docker-compose安装成功\033[0m"

# 开启路由转发
echo "net.ipv4.ip_forward = 1" >> /etc/sysctl.conf
/usr/sbin/sysctl -p
echo -e "\033[32m 开启路由转发成功。\033[0m"

echo "+------------------------------------------------------------------------+"
echo "|            To initialization system all completed !!!    reboot        |"
echo "+------------------------------------------------------------------------+"
