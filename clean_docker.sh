#!/bin/bash

#删除无用的container容器

docker rm `docker ps -a | grep Exited | awk '{print $1}'` 

#删除名称或标签为none的镜像

docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker stop
docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker rm
docker images | grep none | awk '{print $3}' | xargs docker rmi

#删除未使用的volumn
docker volume rm $(docker volume ls -qf dangling=true)

#删除无用的volumns
docker system prune --all --force
