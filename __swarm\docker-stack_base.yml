version: "3.8"

services:
  postgres:
    image: harbor.qdbdtd.com/middleware/postgis:${PG_VERSION}
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    volumes:
      - base-postgres:/var/lib/postgresql/data
    networks:
      - middle
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3