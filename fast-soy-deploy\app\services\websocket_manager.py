from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, List
import json
import asyncio
from app.services.docker_service import docker_service
from app.core.logger import logger

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.container_monitors: Dict[str, bool] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket 客户端连接: {client_id}")
    
    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.container_monitors:
            self.container_monitors[client_id] = False
        logger.info(f"WebSocket 客户端断开: {client_id}")
    
    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送消息失败: {str(e)}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """广播消息给所有连接的客户端"""
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"广播消息失败: {str(e)}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def start_container_monitor(self, container_id: str, client_id: str):
        """开始监控容器状态和日志"""
        self.container_monitors[client_id] = True
        logger.info(f"开始监控容器: {container_id}")
        
        while self.container_monitors.get(client_id, False):
            try:
                # 获取容器统计信息
                stats = docker_service.get_container_stats(container_id)
                
                # 发送状态更新
                await self.send_personal_message({
                    "type": "container_stats",
                    "container_id": container_id,
                    "data": stats
                }, client_id)
                
                await asyncio.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                await self.send_personal_message({
                    "type": "error",
                    "message": f"监控容器失败: {str(e)}"
                }, client_id)
                break
        
        logger.info(f"停止监控容器: {container_id}")
    
    async def start_log_stream(self, container_id: str, client_id: str):
        """开始流式传输容器日志"""
        try:
            container = docker_service.client.containers.get(container_id)
            log_stream = container.logs(stream=True, follow=True, tail=50)
            
            for log_line in log_stream:
                if not self.container_monitors.get(client_id, False):
                    break
                
                await self.send_personal_message({
                    "type": "container_logs",
                    "container_id": container_id,
                    "data": log_line.decode('utf-8', errors='ignore').strip()
                }, client_id)
                
        except Exception as e:
            await self.send_personal_message({
                "type": "error",
                "message": f"获取日志流失败: {str(e)}"
            }, client_id)

manager = ConnectionManager()