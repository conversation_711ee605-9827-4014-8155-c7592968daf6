import docker
import json
import asyncio
from typing import List, Dict, Optional, Any
from docker.errors import DockerException, NotFound, APIError
from sqlalchemy.orm import Session
from app.models.container import ContainerGroup, Container, DeploymentLog
from loguru import logger

class DockerService:
    def __init__(self):
        try:
            self.client = docker.from_env()
            # 测试连接
            self.client.ping()
            logger.info("Docker 客户端连接成功")
        except DockerException as e:
            logger.error(f"Docker 连接失败: {str(e)}")
            raise Exception(f"Docker 连接失败: {str(e)}")
    
    def _log_deployment(self, db: Session, group_id: int, action: str, status: str, message: str):
        """记录部署日志"""
        log = DeploymentLog(
            group_id=group_id,
            action=action,
            status=status,
            message=message
        )
        db.add(log)
        db.commit()
    
    async def create_container_group(self, group: ContainerGroup, db: Session) -> Dict[str, Any]:
        """创建容器组"""
        try:
            # 更新状态为创建中
            group.status = "creating"
            db.commit()
            
            self._log_deployment(db, group.id, "deploy", "running", f"开始创建容器组 {group.name}")
            
            containers_created = []
            
            # 确保网络存在
            try:
                self.client.networks.get(group.network)
            except NotFound:
                logger.info(f"创建网络: {group.network}")
                self.client.networks.create(group.network, driver="bridge")
            
            for i in range(group.replicas):
                container_name = f"{group.name}-{i+1}"
                
                # 准备容器配置
                container_config = {
                    "image": group.image,
                    "name": container_name,
                    "detach": True,
                    "labels": {
                        "group.id": str(group.id),
                        "group.name": group.name,
                        "managed.by": "fast-deploy-admin"
                    },
                    "restart_policy": {"Name": "unless-stopped"}
                }
                
                # 添加环境变量
                if group.environment:
                    container_config["environment"] = group.environment
                
                # 添加端口映射
                if group.ports:
                    container_config["ports"] = group.ports
                
                # 创建并启动容器
                container = self.client.containers.run(**container_config)
                
                # 连接到指定网络
                if group.network != "bridge":
                    try:
                        network = self.client.networks.get(group.network)
                        network.connect(container)
                    except Exception as e:
                        logger.warning(f"连接网络失败: {str(e)}")
                
                # 保存到数据库
                db_container = Container(
                    id=container.id,
                    name=container_name,
                    image=group.image,
                    status="running",
                    group_id=group.id,
                    ports=group.ports,
                    environment=group.environment
                )
                db.add(db_container)
                containers_created.append(container.id)
                
                logger.info(f"创建容器成功: {container_name}")
            
            # 更新组状态
            group.status = "running"
            db.commit()
            
            message = f"成功创建 {len(containers_created)} 个容器"
            self._log_deployment(db, group.id, "deploy", "success", message)
            
            return {
                "success": True,
                "message": message,
                "containers": containers_created
            }
            
        except Exception as e:
            error_msg = f"创建失败: {str(e)}"
            logger.error(error_msg)
            group.status = "error"
            db.commit()
            self._log_deployment(db, group.id, "deploy", "failed", error_msg)
            
            return {
                "success": False,
                "message": error_msg
            }
    
    def get_container_logs(self, container_id: str, tail: int = 100) -> str:
        """获取容器日志"""
        try:
            container = self.client.containers.get(container_id)
            logs = container.logs(tail=tail, timestamps=True)
            return logs.decode('utf-8', errors='ignore')
        except Exception as e:
            logger.error(f"获取日志失败: {str(e)}")
            return f"获取日志失败: {str(e)}"
    
    def get_container_stats(self, container_id: str) -> Dict[str, Any]:
        """获取容器统计信息"""
        try:
            container = self.client.containers.get(container_id)
            stats = container.stats(stream=False)
            
            # 解析统计信息
            cpu_percent = 0.0
            if 'cpu_stats' in stats and 'precpu_stats' in stats:
                cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
                if system_delta > 0:
                    cpu_percent = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100.0
            
            memory_usage = stats.get('memory_stats', {}).get('usage', 0)
            memory_limit = stats.get('memory_stats', {}).get('limit', 0)
            
            return {
                "container_id": container_id,
                "cpu_percent": round(cpu_percent, 2),
                "memory_usage": memory_usage,
                "memory_limit": memory_limit,
                "memory_percent": round((memory_usage / memory_limit * 100) if memory_limit > 0 else 0, 2),
                "status": container.status
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {"error": str(e)}

# 全局实例
docker_service = DockerService()
