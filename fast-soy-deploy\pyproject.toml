[project]
name = "fast-soy-admin"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    { name = "sleep1223", email = "<EMAIL>" },
]
dependencies = [
    "fastapi>=0.111.0",
    "uvicorn>=0.29.0",
    "tortoise-orm[aiosqlite]>=0.20.1",
    "pydantic>=2.7.1",
    "pydantic-settings>=2.2.1",
    "passlib>=1.7.4",
    "pyjwt>=2.8.0",
    "loguru>=0.7.2",
    "aerich>=0.7.2",
    "email-validator>=2.1.1",
    "setuptools>=69.5.1",
    "argon2-cffi>=23.1.0",
    "fastapi-cache2>=0.2.2",
    "redis>=4.6.0",
    "orjson>=3.10.12",
    "alibabacloud-dysmsapi20170525>=3.1.0",
]
requires-python = ">=3.10"
readme = "README.md"
license = { text = "MIT" }

[tool.ruff]
line-length = 120

[tool.ruff.lint]
exclude = ["web", "node_modules", "migrations", "src/experimental", "src/typestubs", "src/oldstuff"]
extend-select = [
    "F",
    "E",
    "W",
    "UP",
    # "I",    # isort
    # "B",    # flake8-bugbear
    # "C4",   # flake8-comprehensions
    # "PGH",  # pygrep-hooks
    # "RUF",  # ruff
    # "W",    # pycodestyle
    # "YTT",  # flake8-2020
]

ignore = [
    "F403",
    "F405",
    "E501", # Line too long
    #    "I001", # isort
    "W293",
]

[tool.ruff.format]
#quote-style = "single"
#indent-style = "tab"
#docstring-code-format = true

[tool.pyright]
include = ["app"]
exclude = ["web",
    "**/node_modules",
    "**/__pycache__",
    "src/experimental",
    "src/typestubs",
]

ignore = [
    "migrations", "src/oldstuff",
]

defineConstant = { DEBUG = false }
stubPath = "src/stubs"

reportMissingImports = true
#reportMissingTypeStubs = false
#
#reportIncompatibleVariableOverride = false

[tool.pdm]
distribution = false

[tool.pdm.build]
includes = []

[[tool.pdm.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
verify_ssl = true

[tool.aerich]
tortoise_orm = "app.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
