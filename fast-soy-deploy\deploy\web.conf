server {
        listen 80;
        server_name localhost;
        location / {
              root /var/www/html/fast-soy-admin;
              index index.html index.htm;
              try_files $uri /index.html;
        }
        location ^~ /api/ {
                proxy_pass http://app:9999;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }
}