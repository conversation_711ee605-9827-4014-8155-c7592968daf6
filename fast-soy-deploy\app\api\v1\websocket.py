from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from app.services.websocket_manager import manager
import asyncio

router = APIRouter()

@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_json()
            
            if data.get("type") == "monitor_container":
                container_id = data.get("container_id")
                if container_id:
                    # 启动容器监控
                    asyncio.create_task(
                        manager.start_container_monitor(container_id, client_id)
                    )
            
            elif data.get("type") == "stream_logs":
                container_id = data.get("container_id")
                if container_id:
                    # 启动日志流
                    asyncio.create_task(
                        manager.start_log_stream(container_id, client_id)
                    )
            
            elif data.get("type") == "stop_monitor":
                manager.container_monitors[client_id] = False
                
    except WebSocketDisconnect:
        manager.disconnect(client_id)