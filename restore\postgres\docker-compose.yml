version: "3"

volumes: 
  base-postgres:
    external: true

services:
  restore_postgres:
    image: harbor.qdbdtd.com/middleware/postgis:${PG_VERSION}
    env_file:
      - ../../base/.env
    volumes:
      - "base-postgres:/var/lib/postgresql/data"
      - "./db:/docker-entrypoint-initdb.d:ro"
    environment:
      - POSTGRES_USER=${POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DATABASE}
