# 北斗天地 中台

## 操作说明
* 通过 git 拉取 指定版本或tag  deploy-gis 工程.  
```sh
   $ git clone --branch <tag-name> https://gitee.com/bjbigdata/deploy-gis.git
```


* 修改配置文件参数`.config.env`,主要有如下参数

```sh
# 矿 ID（根据 cms 矿名表进行映射的 id，每个矿都不同，找研发管理部申请获取矿编码）
export MINECODE=
# 授权配置
# 矿名称，找研发管理部申请获取矿名称
export AUTH_PROJECT_NAME=XXX煤矿
# 同MINECODE
export AUTH_PROJECT_ID=

#  当前服务器可达IP，用于对外暴露服务使用
export BLADE_MINIO_HOST=

#GIS 服务IP
export LY_GIS_HOST=

# 短信服务路径，若无可不配置
export SMS_URL=http://localhost:9001
export SMS_USERNAME=root
export SMS_PASSWORD=123456

#应急广播的第三方URL，用于应急广播发下联动，若无不配置
export MONITOR_FACTORY_URL=http://localhost:9001


# 备份文件存放目录, 根据机器得磁盘情况，指定存储磁盘所在目录
BACKUP_BASE_PATH=/home/<USER>

# 根据部署环境选择授权中心
export PUBLIC_SERVICE_PATH=
export PUBLIC_SERVICE_PORT=
```

* 初始化部署工程
```sh
$ ./auto_deploy.sh deploy -i

###执行命令输入，一路Y/Yes， 下载工程需要git账号，请提前准备
```

* 项目工程特殊处理: 在项目工程中会出现各服务版本持续升级迭代情况，则进行如下操作
```sh
   ## 修改各自服务的版本信息，具体在base/.env, mos/.env, app/.env, video/.env中，并提交gitee
   $ git add .
   $ git commit -am "change version etc..."
   $ git push origin ${version}

   ## 现场工程拉取最新版本,需要用stash命令保留环境配置信息，需要输入部署账号 (OPTIONAL)
   $ git stash
   $ git pull
   $ git stash pop
   ## NOTE：建议以上初始化操作完成后，将现场工程直接push回gitee管理，则更新现场只需要以下命令
   $ git pull

   ## 执行start
   $ ./auto_deploy.sh start
```

---

## 分支说明

- `master` 主分支，可向生产环境交付的最新稳定版本。只有测试通过的dev版本分支可以合并至 master 分支
- `v-*` release tag，通常有多个，稳定的产品tag，用于部署产品化项目，也可以作为基准版本供其它项目使用。
- `feature/*` 功能分支，为正在开发测试中的新功能。完成后可提交 Merge Request，合并至版本分支并测试通过后删除。
- `${PROJECT_NAME}` 项目分支，每个项目单独拉取一个分支维护。

共包含三组可独立部署的服务

- GIS 服务
- 视频服务
- 基准 中台其他服务（Topo、数据接入、用户权限...）

后两者共同依赖 `./base/` 基础服务（数据库、消息队列、缓存、Node-RED 等）

## 环境以及系统要求

使用标准化系统环境，并配置 git 账户信息

系统要求

- [`Docker Engine`](https://docs.docker.com/engine/release-notes/) Version `20.10.14` 以上
- [`Docker Compose`](https://docs.docker.com/compose/release-notes/) Version `1.27.4` 以上
- [`git`](https://git-scm.com/) Version `2.24` 以上
- 登录 Docker 仓库 ([Harbor]())
- 配置 Docker Daemon：log-opts, data-root, registry-mirrors 等
- 配置 Git 账号信息、添加 ssh key 等

检查清单

- [ ] 数据库连接最大数设置（默认 100，根据现场环境适当调大连接数，每个 hasura 默认 50 个连接）
- [ ] 数据库备份 （使用工具备份， 不要直接拷贝文件夹）
- [ ] 数据库定时 vacuum
- [ ] [Hasura 检查清单](https://hasura.io/docs/1.0/graphql/manual/deployment/production-checklist.html#id1)


---


## 端口说明

以下均为端口使用（转发）默认值


### 视频服务

- `8988`: 摄像头管理页面
- `1935`: SRS 监听端口
- `1980`: SRS Nginx 服务端口 (HTTP 流媒体)

### 中台其他服务

- `5432`: 中台 PostgreSQL 数据库
- `5672`: 中台 RabbitMQ
- `5673`: 中台 BladeX RabbitMQ
- `8001`: 前端页面
- `8002`: saber 前端页面
- `9003`: Data Monitor 服务

以下服务端口可在 `.config.env` 中修改

```sh
# RabbitMQ 配置
# RabbitMQ 配置
export PORT_RABBITMQ=5672
export WEW_PORT_RABBITMQ=5672
export PORT_BLADEX_RABBITMQ=5673

# Data Monitor 配置
export PORT_DATA_MONITOR=9003

# 前端配置
export PORT_FRONTEND=8001
export PORT_FRONTEND_SABER=8002
```

均可在对应目录 `.env` 文件中修改

---

## 页面地址

以下地址为 `8001` 端口地址

- `/rabbit/`: RabbitMQ
- `/data-gw/admin/`: Node-gw

---

### SSH 服务

为方便调试，可以开启连接至 Docker `middle` 网络中的 ssh 服务，并通过端口转发调试 Docker 网络中的服务。

详见 [SSH 端口转发](./ssh/README.md)

---

## 反向代理

如需对 `8001` 端口的 Nginx 服务添加自定义反向代理，请在 `./mos/config/nginx.conf.d/` 中添加 Nginx 配置文件或使用 `ln` 创建硬链，并重启 Nginx 服务。

下面的例子是配置 `/hls` 反向代理至 SRS

 `hls.conf`

```Nginx
set $upstream_hls_service "http://video-srs:8080";
location /hls {
  rewrite /hls/(.+) /$1 break;
  proxy_pass $upstream_hls_service;
}
```

如果反向代理需要使用域名，最好将域名作为变量放在 location 块之外，避免因无法解析域名造成 Nginx 服务无法启动。

---

## 文件说明

```sh
├─ base                                          #基础服务部署文档             
│  ├─ .env                                       #环境变量               
│  ├─ changeset                                  #SQL语句滚动更新与脚本滚动更新                   
│  │  ├─ init                                    #存放数据库初始化语句与模块初始化脚本                   
│  │  │  ├─ init.sql                             #数据库初始化语句                          
│  │  │  └─ init.topo.sh                         #topo模块初始化                              
│  │  ├─ vx.x.x                                  #deploy-gis大版本号                      
│  │  │  ├─ base                                 #MOS基础模块的文件夹,里面放up/down.sql                      
│  │  │  ├─ <module>                             #MOS子模块的名称                            
│  │  │  │  ├─ vx.x.x                            #该大版本的deploy-gis对应的MOS子模块是哪个版本号,仅做记录                            
│  │  │  │  ├─ down.sql                          #MOS子模块的down语句                             
│  │  │  │  └─ up.sql                            #MOS子模块的up语句                          
│  ├─ config                                     #基础服务配置文件                  
│  │  ├─ influxdb.conf                           #InfluxDB 配置文件                            
│  │  └─ redis.template                          #Redis 配置文件模板                             
│  ├─ docker-compose.yml                         #容器编排文件                              
│  ├─ hasura                                     #Hasura 迁移文件                  
│  │  ├─ config.yaml                             #Hasura 配置                          
│  │  └─ metadata                                #Hasura 元数据                       
│  └─ README.md                                  #说明文档                     
├─ gis                                           #GIS 服务部署材料            
│  ├─ docker-compose.yml                         #GIS 服务容器编排文件                              
│  └─ README.md                                  #GIS 服务部署文档                     
├─ mos                                           #MOS 服务部署材料            
│  ├─ .env                                       #MOS 环境变量                
│  ├─ config                                     #MOS 服务配置文件                  
│  │  ├─ data-access                             #数据接入前端配置                          
│  │  │  └─ data_access_init.sql                 #数据接入前端配置文件                                      
│  │  ├─ nginx-default.conf                      #Nginx 默认配置文件                                 
│  │  └─ nginx.conf                              #Nginx 代理配置文件                         
│  ├─ docker-compose.yml                         #MOS 中台服务容器编排文件                             
│  ├─ project-data                               #权限配置                        
│  │  ├─ .gitkeep                                #                       
│  │  ├─ licenceRepository                       #                                
│  │  │  └─ .gitkeep                             #                          
│  │  └─ process-engine                          #权限配置默认数据                             
│  │     └─ service-authorization.db             #权限配置默认数据文件                                          
│  └─ README.md                                  #MOS说明文档                     
├─ restore                                       #                
│  └─ postgres                                   #                    
│     ├─ .env                                    #环境变量                   
│     ├─ db                                      #                 
│     │  └─ .keep                                #                       
│     ├─ docker-compose.yml                      #容器编排文件                                 
│     └─ README.md                               #说明文档                        
├─ scripts                                       #部署脚本                
├─ ssh                                           #SSH 服务部署材料            
│  ├─ docker-compose.yml                         #SSH 服务容器编排文件                              
│  └─ README.md                                  #SSH 服务说明文档                     
├─ video                                         #视频服务部署材料              
│   ├─ .env                                      #视频服务环境变量                 
│   └─ docker-compose.yml                        #视频服务容器编排文件                               
├─ .config.env                                   #全局环境变量                    
├─ auto_deploy.sh                                #自动部署脚本                       
├─ centos7-init.sh                               #Cent OS 初始化脚本                        
├─ CHANGELOG.md                                  #升级说明文档                     
├─ docker-install.sh                             #Docker 安装脚本                          
├─ harbor-login.sh.x                             #Harbor 登陆脚本                          
└─ README.md                                     #说明文档
```


---

## 中台的手动部署步骤

### 场景一: 在测试环境上全新部署
* 开发人员确定此次测试的版本号例如为v1.0.0,如有需要,在base/changeset下建立相关版本号的up/down.sql
* 开发人员告知测试人员,此次测试的版本为v1.0.0,该版本位于哪个git分支上,无需打tag
* 测试人员从git拉取目标分支 git clone --branch dev https://gitee.com/bjbigdata/deploy-gis.git
* 修改.config.env
* 执行sh auto_deploy.sh deploy -i进行安装
* 根据脚本提示, 手动输入版本号v1.0.0进行剩余的安装流程


### 场景二: 升级测试环境用于提测(假如当前测试环境为v1.0.0,新功能开发完毕为v1.1.0)
* 开发人员在base/changeset/下建立v1.1.0目录,建立base目录和子模块目录并填写up/down.sql,并在子模块目录下新建个文件,文件名命名为该模块的版本号,仅做标记而已
* 测试人员从git拉取目标分支 git clone --branch dev  https://gitee.com/bjbigdata/deploy-gis.git
* 修改.config.env
* 执行sh auto_deploy.sh update
* 根据脚本提示, 输入版本号v1.1.0进行升级  


### 场景三: 在场景二的基础上,持续进行bug fix
* 如果bug fix需要修改数据库,开发人员应手动修改测试环境的数据库结构,并修改base/changeset/v1.1.0/up.sql
* 开发人员在deploy-gis上修改bug fix版本所需的docker镜像版本号, 提交至git,无需打tag,避免tag上出现大量的测试用tag号 
* 测试人员在测试环境上手动clone最新的开发分支,或执行git pull
* 检查.config.env是否配置正确
* 执行sh auto_deploy.sh restart重启中台服务  
注: 该场景下不推荐每进行一次bug fix就在base/changeset下新建一个版本号,会导致版本号过多不易维护


### 场景四:在全新生产环境上部署
* 测试完毕后,开发人员在git上打tag号,tag号命名应与base/changeset下的版本号命名一致
* 运维人员上传或git拉取目标tag的部署材料至linux服务器
    * 方法1: 从git拉取目标版本 git clone --branch &lt;tag-name&gt; https://gitee.com/bjbigdata/deploy-gis.git
    * 方法2: 上传目标版本的deploy-gis到本地
* 修改.config.env
* 执行sh auto_deploy.sh deploy -i进行全新安装
* 根据脚本提示,确认自动识别的deploy-gis版本号是否正确,如不正确,应当通知开发人员重新打正确的tag号
* 配置minio存贮bucket的Policy
    * 1 打开ssh代理，进入到部署目录，例如:/hom/deploy-gis  执行: ./auto_deploy ssh start
    * 2 在本地打开powershell，输入命令： ssh -L 9000:bladex-minio:9000   -p 2222 root@服务器IP 密码：bdtd2106
      ![代理页面](img.png)
    * 3 打开浏览器访问:http://localhost:9000   账号:minio 密码:bGVhbmlvdA
      ![登录成功页面](img_1.png)
    * 4 点击对应bucket旁边的设置
      ![设置按钮](img_2.png)
    * 5 选择edit policy ,点击add
      ![img_3.png](img_3.png)
    * 6 默认添加一条policy配置
      ![img_4.png](img_4.png)
    * 7 退出powershell,进入服务器部署目录,例如:/home/<USER>/auto_deploy ssh stop 命令关闭ssh代理  
### 场景五: 生产环境升级
* 测试完毕后,开发人员在git上打tag号,tag号命名应与base/changeset下的版本号命名一致
* 运维人员上传或git拉取目标版本的deploy-gis至linux服务器
    * 方法1: 从git仅拉取目标版本 git clone --branch &lt;tag-name&gt; https://gitee.com/bjbigdata/deploy-gis.git
    * 方法2: 上传目标版本的deploy-gis到本地
    * 方法3: 在旧版本的deploy-gis上执行"sh auto_deploy.sh update -g -t &lt;tag-name&gt;;"以切换部署材料的版本号,文件可能会冲突导致切换失败
* 查看.config.env配置项是否正确
* 在新版本deploy-gis目录中执行$: sh auto_deploy.sh update 进行升级, 可以加上参数-r进行不停机升级
* 根据脚本提示,确认自动识别的deploy-gis版本号是否正确,如不正确,应当通知开发人员重新打正确的tag号

---



