package services

import (
    "deploy-manager/internal/database"
    "deploy-manager/internal/docker"
    "deploy-manager/internal/models"
    "fmt"
    "time"
)

type ServiceManager struct {
    dockerClient *docker.Client
}

// NewServiceManager 创建服务管理器
func NewServiceManager() (*ServiceManager, error) {
    dockerClient, err := docker.NewClient()
    if err != nil {
        return nil, err
    }
    
    return &ServiceManager{
        dockerClient: dockerClient,
    }, nil
}

// GetAllServices 获取所有服务
func (sm *ServiceManager) GetAllServices() ([]models.Service, error) {
    var services []models.Service
    if err := database.DB.Preload("Groups").Find(&services).Error; err != nil {
        return nil, err
    }
    
    // 更新服务状态
    for i := range services {
        status, _ := sm.dockerClient.GetServiceStatus(&services[i])
        services[i].Status = status
    }
    
    return services, nil
}

// GetServicesByGroup 根据分组获取服务
func (sm *ServiceManager) GetServicesByGroup(groupName string) ([]models.Service, error) {
    var group models.ServiceGroup
    if err := database.DB.Where("name = ?", groupName).Preload("Services").First(&group).Error; err != nil {
        return nil, err
    }
    
    // 更新服务状态
    for i := range group.Services {
        status, _ := sm.dockerClient.GetServiceStatus(&group.Services[i])
        group.Services[i].Status = status
    }
    
    return group.Services, nil
}

// GetService 获取单个服务
func (sm *ServiceManager) GetService(id uint) (*models.Service, error) {
    var service models.Service
    if err := database.DB.Preload("Groups").Preload("Configurations").First(&service, id).Error; err != nil {
        return nil, err
    }
    
    // 更新服务状态
    status, _ := sm.dockerClient.GetServiceStatus(&service)
    service.Status = status
    
    return &service, nil
}

// CreateService 创建服务
func (sm *ServiceManager) CreateService(service *models.Service) error {
    return database.DB.Create(service).Error
}

// UpdateService 更新服务
func (sm *ServiceManager) UpdateService(service *models.Service) error {
    return database.DB.Save(service).Error
}

// DeleteService 删除服务
func (sm *ServiceManager) DeleteService(id uint) error {
    service, err := sm.GetService(id)
    if err != nil {
        return err
    }
    
    // 如果服务正在运行，先停止
    if service.Status == "running" {
        if err := sm.StopService(id); err != nil {
            return fmt.Errorf("failed to stop service before deletion: %v", err)
        }
    }
    
    return database.DB.Delete(&models.Service{}, id).Error
}

// StartService 启动服务
func (sm *ServiceManager) StartService(id uint) error {
    service, err := sm.GetService(id)
    if err != nil {
        return err
    }
    
    if !service.Enabled {
        return fmt.Errorf("service is disabled")
    }
    
    // 检查依赖服务
    if err := sm.checkDependencies(service); err != nil {
        return fmt.Errorf("dependency check failed: %v", err)
    }
    
    // 记录部署开始
    deployment := &models.Deployment{
        ServiceID: service.ID,
        Action:    "start",
        Status:    "running",
        StartedAt: time.Now(),
    }
    database.DB.Create(deployment)
    
    // 启动Docker容器
    if err := sm.dockerClient.StartService(service); err != nil {
        deployment.Status = "failed"
        deployment.ErrorMessage = err.Error()
        now := time.Now()
        deployment.CompletedAt = &now
        database.DB.Save(deployment)
        return err
    }
    
    // 更新服务状态
    service.Status = "running"
    if err := sm.UpdateService(service); err != nil {
        return err
    }
    
    // 更新部署记录
    deployment.Status = "success"
    deployment.ContainerID = service.ContainerID
    now := time.Now()
    deployment.CompletedAt = &now
    database.DB.Save(deployment)
    
    return nil
}

// StopService 停止服务
func (sm *ServiceManager) StopService(id uint) error {
    service, err := sm.GetService(id)
    if err != nil {
        return err
    }
    
    // 记录部署开始
    deployment := &models.Deployment{
        ServiceID: service.ID,
        Action:    "stop",
        Status:    "running",
        StartedAt: time.Now(),
    }
    database.DB.Create(deployment)
    
    // 停止Docker容器
    if err := sm.dockerClient.StopService(service); err != nil {
        deployment.Status = "failed"
        deployment.ErrorMessage = err.Error()
        now := time.Now()
        deployment.CompletedAt = &now
        database.DB.Save(deployment)
        return err
    }
    
    // 更新服务状态
    service.Status = "stopped"
    if err := sm.UpdateService(service); err != nil {
        return err
    }
    
    // 更新部署记录
    deployment.Status = "success"
    now := time.Now()
    deployment.CompletedAt = &now
    database.DB.Save(deployment)
    
    return nil
}

// RestartService 重启服务
func (sm *ServiceManager) RestartService(id uint) error {
    if err := sm.StopService(id); err != nil {
        return err
    }
    
    // 等待一秒确保容器完全停止
    time.Sleep(1 * time.Second)
    
    return sm.StartService(id)
}

// StartGroup 启动服务组
func (sm *ServiceManager) StartGroup(groupName string) error {
    services, err := sm.GetServicesByGroup(groupName)
    if err != nil {
        return err
    }
    
    // 按依赖顺序启动服务
    for _, service := range services {
        if service.Enabled && service.Status != "running" {
            if err := sm.StartService(service.ID); err != nil {
                return fmt.Errorf("failed to start service %s: %v", service.Name, err)
            }
        }
    }
    
    return nil
}

// StopGroup 停止服务组
func (sm *ServiceManager) StopGroup(groupName string) error {
    services, err := sm.GetServicesByGroup(groupName)
    if err != nil {
        return err
    }
    
    // 逆序停止服务
    for i := len(services) - 1; i >= 0; i-- {
        service := services[i]
        if service.Status == "running" {
            if err := sm.StopService(service.ID); err != nil {
                return fmt.Errorf("failed to stop service %s: %v", service.Name, err)
            }
        }
    }
    
    return nil
}

// GetServiceLogs 获取服务日志
func (sm *ServiceManager) GetServiceLogs(id uint, tail string) (string, error) {
    service, err := sm.GetService(id)
    if err != nil {
        return "", err
    }
    
    return sm.dockerClient.GetServiceLogs(service, tail)
}

// checkDependencies 检查服务依赖
func (sm *ServiceManager) checkDependencies(service *models.Service) error {
    for _, depID := range service.DependsOn {
        depService, err := sm.GetService(depID)
        if err != nil {
            return fmt.Errorf("dependency service %d not found", depID)
        }
        
        if depService.Status != "running" {
            return fmt.Errorf("dependency service %s is not running", depService.Name)
        }
    }
    
    return nil
}

// Close 关闭服务管理器
func (sm *ServiceManager) Close() error {
    return sm.dockerClient.Close()
}
