from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.models.container import ContainerGroup, Container, DeploymentLog
from app.schemas.container import (
    ContainerGroupCreate, 
    ContainerGroupResponse, 
    ContainerGroupUpdate,
    ScaleRequest,
    LogResponse,
    ContainerStatsResponse
)
from app.services.docker_service import docker_service
from app.core.logger import logger

router = APIRouter()

@router.post("/groups", response_model=ContainerGroupResponse)
async def create_container_group(
    group_data: ContainerGroupCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """创建容器组"""
    # 检查名称是否已存在
    existing = db.query(ContainerGroup).filter(ContainerGroup.name == group_data.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="容器组名称已存在")
    
    # 创建数据库记录
    db_group = ContainerGroup(**group_data.dict())
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    
    # 后台任务创建容器
    background_tasks.add_task(
        docker_service.create_container_group,
        db_group,
        db
    )
    
    logger.info(f"创建容器组任务已提交: {group_data.name}")
    return db_group

@router.get("/groups", response_model=List[ContainerGroupResponse])
def list_container_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取容器组列表"""
    query = db.query(ContainerGroup)
    
    if status:
        query = query.filter(ContainerGroup.status == status)
    
    groups = query.offset(skip).limit(limit).all()
    return groups

@router.get("/groups/{group_id}", response_model=ContainerGroupResponse)
def get_container_group(group_id: int, db: Session = Depends(get_db)):
    """获取单个容器组详情"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    return group

@router.put("/groups/{group_id}", response_model=ContainerGroupResponse)
def update_container_group(
    group_id: int,
    group_data: ContainerGroupUpdate,
    db: Session = Depends(get_db)
):
    """更新容器组"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    
    update_data = group_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(group, field, value)
    
    db.commit()
    db.refresh(group)
    return group

@router.post("/groups/{group_id}/scale")
async def scale_container_group(
    group_id: int,
    scale_data: ScaleRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """扩缩容容器组"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    
    # 后台执行扩缩容
    background_tasks.add_task(
        docker_service.scale_container_group,
        group_id,
        scale_data.replicas,
        db
    )
    
    logger.info(f"扩缩容任务已提交: 组ID={group_id}, 目标副本数={scale_data.replicas}")
    return {"message": "扩缩容任务已提交", "target_replicas": scale_data.replicas}

@router.post("/groups/{group_id}/start")
async def start_container_group(
    group_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """启动容器组"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    
    background_tasks.add_task(
        docker_service.start_container_group,
        group_id,
        db
    )
    
    return {"message": "启动任务已提交"}

@router.post("/groups/{group_id}/stop")
async def stop_container_group(
    group_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """停止容器组"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    
    background_tasks.add_task(
        docker_service.stop_container_group,
        group_id,
        db
    )
    
    return {"message": "停止任务已提交"}

@router.delete("/groups/{group_id}")
async def delete_container_group(
    group_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """删除容器组"""
    group = db.query(ContainerGroup).filter(ContainerGroup.id == group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="容器组不存在")
    
    # 先停止容器组
    if group.status == "running":
        await docker_service.stop_container_group(group_id, db)
    
    # 删除所有容器
    containers = db.query(Container).filter(Container.group_id == group_id).all()
    for container in containers:
        try:
            docker_container = docker_service.client.containers.get(container.id)
            docker_container.remove(force=True)
        except Exception as e:
            logger.warning(f"删除容器失败: {str(e)}")
    
    # 删除数据库记录
    db.delete(group)
    db.commit()
    
    return {"message": "容器组已删除"}

@router.get("/containers/{container_id}/logs")
def get_container_logs(
    container_id: str, 
    tail: int = Query(100, ge=1, le=1000)
):
    """获取容器日志"""
    logs = docker_service.get_container_logs(container_id, tail)
    return {"logs": logs, "container_id": container_id}

@router.get("/containers/{container_id}/stats")
def get_container_stats(container_id: str):
    """获取容器统计信息"""
    stats = docker_service.get_container_stats(container_id)
    return stats

@router.get("/groups/{group_id}/logs")
def get_deployment_logs(
    group_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取部署日志"""
    logs = db.query(DeploymentLog).filter(
        DeploymentLog.group_id == group_id
    ).order_by(
        DeploymentLog.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    return logs